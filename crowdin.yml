#
# Basic Crowdin CLI configuration
# See https://crowdin.github.io/crowdin-cli/configuration for more information
# See https://support.crowdin.com/developer/configuration-file/ for all available options
#

#
# Your Crowdin credentials
#
"project_id": "754659"
"api_token": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
"base_path": "."
"base_url": "https://api.crowdin.com"

#
# Defines whether to preserve the original directory structure in the Crowdin project
# Recommended to set to true
#
"preserve_hierarchy": false

#
# Files configuration.
# See https://support.crowdin.com/developer/configuration-file/ for all available options
#
files: [
  {
    #
    # Source files filter
    # e.g. "/resources/en/*.json"
    #
    "source": "/src/locales/zh-CN.json",

    #
    # Translation files filter
    # e.g. "/resources/%two_letters_code%/%original_file_name%"
    #
    "translation": "/src/locales/%locale%.json",
  }
]
