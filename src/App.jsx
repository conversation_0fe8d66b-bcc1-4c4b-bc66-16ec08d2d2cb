import { useState, useContext } from 'react'
import './App.css'
import React from 'react';

import { theme, ConfigProvider ,Tooltip} from 'antd';
import { KeyboardProvider } from './components/Keyboard/KeyboardContext';
import { HandleDeviceProvider } from './components/HIDDevice/HandleDeviceContext';
import LayoutRoot from './components/Layout/LayoutRoot';
import { useTranslation } from 'react-i18next';
import Theme from './components/Theme';

const getSystemShortcut = () => {
  const platform = window.navigator.platform.toLowerCase();
  if (platform.includes('mac')) {
    return 'Command';
  }
  // Default to Windows/Linux
  return 'L-Ctrl';
};

const App = () => {
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const { t } = useTranslation();
  const [shortcutKey, setShortcutKey] = useState(getSystemShortcut());

  React.useEffect(() => {
    if (import.meta.env.VITE_APP_ENV !== 'Dev') {
      if (window.location.protocol === 'http:') {
        window.location.href = window.location.href.replace('http:', 'https:');
      }
    }
  }, []);

  React.useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  React.useEffect(() => {
    document.title = t('meta.title')
  }, [t])

  React.useEffect(() => {
    // Update shortcutKey if platform changes (e.g., user agent spoofing or browser reload)
    setShortcutKey(getSystemShortcut());
  }, []);

  return (
    <ConfigProvider theme={{
      algorithm: theme.darkAlgorithm,
      token: {
        colorPrimary: Theme.colorPrimary,
      },
      "components": {
        "Menu": {
          "darkItemSelectedBg": "#17181C",
        }
      },
    }}>
      <KeyboardProvider>
        <HandleDeviceProvider>
          <div style={{display: windowWidth >= 1650 ? 'block' : 'none'}}>
            <LayoutRoot  />
          </div>
          <div style={{display: windowWidth >= 1650 ? 'none' : 'flex'}}>
            <div style={{
              height: '100vh',
              width: '100vw',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              gap: '20px',
              padding: '20px',
              textAlign: 'center',
              background: "#161720",
              color: 'white'
            }}>
              <div>{t('app.window_too_small')}</div>
              <div>
                {shortcutKey === 'Command'
                ? (
                  <>
                    {t('app.resize_window_windows_1')} <kbd>⌘ Cmd</kbd> <kbd>-</kbd> {t('app.resize_window_windows_2')}
                  </>
                )
                : (
                  <>
                    {t('app.resize_window_windows_1')} <kbd>L-Ctrl</kbd> <kbd>-</kbd> {t('app.resize_window_windows_2')}
                  </>
                )
              }
              </div>
            </div>
          </div>
        </HandleDeviceProvider>
      </KeyboardProvider>
    </ConfigProvider>
  );
};
export default App;
