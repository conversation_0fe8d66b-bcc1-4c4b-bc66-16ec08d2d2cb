import { useState, useEffect } from 'react';
import { Resizable } from 'react-resizable';
import DksAddIcon from '../../../assets/dksAddIcon.svg';

const DksConfigItem = ({getFinalPointData, keyIndex, keycap_config}) => {
  // 解析 keycap_config 为二进制字符串
  const binaryStr = parseInt(keycap_config, 16).toString(2).padStart(8, '0');
  const pointData = binaryStr.split('');

  const calculateWidth = (pointData, index) => {
    if (pointData[index] === '0') return 24;

    // 根据后续的控制位计算宽度
    const controlBits = pointData.slice(4);
    switch(index) {
      case 0: // first point
        if (controlBits[0] === '1') {
          if (controlBits[1] === '1' && controlBits[2] === '1') {
            if (pointData[1] === '0' && pointData[2] === "0") {
              return 412
            } else if (pointData[1] === '1' && pointData[2] === "0") {
              return 124
            } else if (pointData[1] === '0' && pointData[2] === "1") {
              return 268
            } else if (pointData[1] === '1' && pointData[2] === "1") {
              return 124
            }
          }
          if (controlBits[1] === '1' && controlBits[2] === '0') {
            if (pointData[1] === '0' && pointData[2] === "0") {
              return 268
            } else if (pointData[1] === '1' && pointData[2] === "0") {
              return 124
            } else if (pointData[1] === '0' && pointData[2] === "1") {
              return 268
            } else if (pointData[1] === '1' && pointData[2] === "1") {
              return 124
            }
          }
          if (controlBits[1] === '0' && controlBits[2] === '0') return 124;
          return 124;
        }
        return 24;
      case 1: // second point
        if (controlBits[1] === '1') {
          if (controlBits[2] === '1') {
            if (pointData[2] === "0") {
              return 268
            } else if (pointData[2] === "1") {
              return 124
            }
          }
          return 124;
        }
        return 24;
      case 2: // third point
        if (controlBits[2] === '1') return 124;
        return 24;
      default: // fourth point
        return 24;
    }
  };

  const initialState = {
    width: {
      first: calculateWidth(pointData, 0) || 24,
      second: calculateWidth(pointData, 1) || 24,
      third: calculateWidth(pointData, 2) || 24,
      fourth: calculateWidth(pointData, 3) || 24
    },
    tmpWidth: {
      first: calculateWidth(pointData, 0) || 24,
      second: calculateWidth(pointData, 1) || 24,
      third: calculateWidth(pointData, 2) || 24,
      fourth: calculateWidth(pointData, 3) || 24
    },
    pointWrapper: {
      first: pointData[0] === '1',
      second: pointData[1] === '1',
      third: pointData[2] === '1',
      fourth: pointData[3] === '1'
    },
    tmpPointWrapper: {
      first: false,
      second: false,
      third: false,
      fourth: false
    }
  };

  const [state, setState] = useState(initialState);
  const [height, setHeight] = useState(24);

  useEffect(() => {
    const pointData = getPointData();
    getFinalPointData?.(pointData, keyIndex);
  }, [state.pointWrapper, state.width]);

  const getPointData = () => {
    let pointData = [0, 0, 0, 0, 0, 0, 0, 0];
    if (state.pointWrapper.first == true) {
      pointData[0] = 1;
    }
    if (state.pointWrapper.second == true) {
      pointData[1] = 1;
    }
    if (state.pointWrapper.third == true) {
      pointData[2] = 1;
    }
    if (state.pointWrapper.fourth == true) {
      pointData[3] = 1;
    }

    if (state.width.first == 124) {
      pointData[4] = 1;
    } else if (state.width.first == 268) {
      pointData[1] = 0;
      pointData[4] = 1;
      pointData[5] = 1;
    } else if (state.width.first == 412) {
      pointData[1] = 0;
      pointData[2] = 0;
      pointData[4] = 1;
      pointData[5] = 1;
      pointData[6] = 1;
    }

    if (state.width.second == 124) {
      pointData[5] = 1;
    } else if (state.width.second == 268) {
      pointData[2] = 0;
      pointData[5] = 1;
      pointData[6] = 1;
    }

    if (state.width.third == 124) {
      pointData[2] = 1;
      pointData[6] = 1;
    }

    return pointData.join('');
  }

  const handlePointWrapper = (point, status) => {
    setState(prev => ({
      ...prev,
      pointWrapper: {
        ...prev.pointWrapper,
        [point]: status
      },
      width: {
        ...prev.width,
        [point]: 24
      }
    }));
    getPointData();
  };

  const updateWidth = (point, width) => {
    setState(prev => ({
      ...prev,
      width: {
        ...prev.width,
        [point]: width
      }
    }));
    getPointData();
  };

  const updateTmpWidth = (point, width) => {
    setState(prev => ({
      ...prev,
      tmpWidth: {
        ...prev.tmpWidth,
        [point]: width
      }
    }));
  };

  const getNextActivePoint = (pointKey) => {
    const points = ["01", "02", "03", "04"];
    const currentIndex = points.indexOf(pointKey);

    for (let i = currentIndex + 1; i < points.length; i++) {
      const point = getPointName(points[i]);
      if (state.pointWrapper[point]) {
        return points[i];
      }
    }
    return null;
  };

  const handleResize = (e, data, pointKey) => {
    const width = data.size.width;
    const point = getPointName(pointKey);
    const nextActivePoint = getNextActivePoint(pointKey);

    // 检查是否会超过下一个激活点的位置
    if (nextActivePoint) {
      const nextMarginLeft = getMarginLeft(nextActivePoint);
      if (width + getMarginLeft(pointKey) > nextMarginLeft) {
        return;
      }
    }

    updateWidth(point, width);

    let tmpWidth = 24;
    if (width < 70) {
      tmpWidth = 24;
    } else if (width < 190) {
      tmpWidth = 124;
    } else if (width < 310) {
      tmpWidth = 268;
    } else {
      tmpWidth = 412;
    }

    updateTmpWidth(point, tmpWidth);
    setHeight(data.size.height);
  };

  const handleResizeStop = (e, { size }, pointKey) => {
    const width = size.width;
    const point = getPointName(pointKey);
    const nextActivePoint = getNextActivePoint(pointKey);

    let finalWidth = 24;
    if (width < 70) {
      finalWidth = 24;
    } else if (width < 190) {
      finalWidth = 124;
    } else if (width < 310) {
      finalWidth = 268;
    } else {
      finalWidth = 412;
    }

    // 检查最终宽度是否会超过下一个激活点的位置
    if (nextActivePoint) {
      const nextMarginLeft = getMarginLeft(nextActivePoint);
      if (finalWidth + getMarginLeft(pointKey) > nextMarginLeft) {
        finalWidth = nextMarginLeft - getMarginLeft(pointKey);
      }
    }

    updateWidth(point, finalWidth);

    setState(prev => ({
      ...prev,
      tmpPointWrapper: {
        first: false,
        second: false,
        third: false,
        fourth: false
      }
    }));
  };

  const handlePointClick = (pointKey) => {
    const point = getPointName(pointKey);
    handlePointWrapper(point, true);
  };

  const handlePointWrapperClick = (e, pointKey) => {
    if (e.target.closest('.resize-handle')) {
      return;
    }
    const point = getPointName(pointKey);
    handlePointWrapper(point, false);
  };

  const handleResizeStart = (pointKey) => {
    const point = getPointName(pointKey);
    setState(prev => ({
      ...prev,
      tmpPointWrapper: {
        ...prev.tmpPointWrapper,
        [point]: true
      }
    }));
  };

  const getPointName = (pointKey) => {
    const pointMap = {
      "01": "first",
      "02": "second",
      "03": "third",
      "04": "fourth"
    };
    return pointMap[pointKey];
  };

  const getMaxConstraints = (pointKey) => {
    const maxMap = {
      "01": [412, 412],
      "02": [268, 268],
      "03": [124, 124],
      "04": [24, 24]
    };
    return maxMap[pointKey];
  };

  const getMarginLeft = (pointKey) => {
    const marginMap = {
      // "01": 0,
      // "02": 120,
      // "03": 240,
      // "04": 360
      "01": 0,
      "02": 144,
      "03": 288,
      "04": 432

    };
    return marginMap[pointKey];
  };

  const renderResizable = (pointKey) => {
    const point = getPointName(pointKey);
    const width = state.width[point];
    const marginLeft = getMarginLeft(pointKey);
    const maxConstraints = getMaxConstraints(pointKey);
    const nextActivePoint = getNextActivePoint(pointKey);

    // 如果有下一个激活点，调整最大约束
    const adjustedMaxConstraints = nextActivePoint ?
      [getMarginLeft(nextActivePoint) - marginLeft, maxConstraints[1]] :
      maxConstraints;

    return state.pointWrapper[point] && (
      <Resizable
        height={height}
        width={width}
        onResize={(e, data) => handleResize(e, data, pointKey)}
        onResizeStop={(e, data) => handleResizeStop(e, data, pointKey)}
        onResizeStart={() => handleResizeStart(pointKey)}
        handle={<div className="resize-handle">)</div>}
        maxConstraints={adjustedMaxConstraints}
        minConstraints={[24, 24]}
        axis='x'
      >
        <div
          className='status-point-wrapper active'
          style={{width, zIndex: 9, marginLeft}}
          onClick={(e) => handlePointWrapperClick(e, pointKey)}
        />
      </Resizable>
    );
  };

  const renderTmpPoint = (pointKey) => {
    const point = getPointName(pointKey);
    const width = state.tmpWidth[point];
    const marginLeft = getMarginLeft(pointKey);

    return state.tmpPointWrapper[point] && (
      <div
        className='tmp-status-point-wrapper active'
        style={{width, marginLeft}}
      />
    );
  };

  const renderPoint = (pointKey) => {
    const point = getPointName(pointKey);
    const isWrapped = state.pointWrapper[point];

    if (isWrapped) {
      return (
        <div style={{width: '24px', height: '24px', display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
          <div
            className='status-point'
            style={{cursor: 'pointer', zIndex: 9}}
            onClick={(e) => handlePointWrapperClick(e, pointKey)}
          />
        </div>
      );
    }

    return (
      <img
        src={DksAddIcon}
        alt='dks-add-icon'
        style={{cursor: 'pointer'}}
        onClick={() => handlePointClick(pointKey)}
      />
    );
  };

  return (
    <div className='dks-setting-bar'>
      {renderResizable("01")}
      {renderResizable("02")}
      {renderResizable("03")}
      {renderResizable("04")}
      {renderTmpPoint("01")}
      {renderTmpPoint("02")}
      {renderTmpPoint("03")}
      {renderTmpPoint("04")}
      {renderPoint("01")}
      {renderPoint("02")}
      {renderPoint("03")}
      {renderPoint("04")}
    </div>
  );
};

export default DksConfigItem;
