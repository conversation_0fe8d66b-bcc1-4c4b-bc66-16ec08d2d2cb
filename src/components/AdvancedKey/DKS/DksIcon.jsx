const DksIcon = ({ color, fillOpacity }) => {
  return (
    <svg width="37" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.51416 27.0877H32.6591V28H6.51416V27.0877Z" fill={color} fillOpacity={fillOpacity}/>
    <path d="M28.643 24.0286C27.2059 24.0286 26.1683 23.6708 25.5303 22.9553C24.8982 22.2397 24.5821 21.1008 24.5821 19.5385V18H27.6948V19.9678C27.6948 20.3315 27.7485 20.6178 27.8558 20.8265C27.9691 21.0292 28.1629 21.1306 28.4372 21.1306C28.7235 21.1306 28.9202 21.0471 29.0276 20.8801C29.1409 20.7132 29.1975 20.4389 29.1975 20.0572C29.1975 19.5742 29.1498 19.1717 29.0544 18.8497C28.959 18.5218 28.792 18.2117 28.5535 17.9195C28.321 17.6213 27.996 17.2755 27.5786 16.8819L26.1653 15.5403C25.1099 14.5444 24.5821 13.4055 24.5821 12.1234C24.5821 10.7818 24.8922 9.75909 25.5124 9.05546C26.1385 8.35182 27.0419 8 28.2226 8C29.6656 8 30.6883 8.38462 31.2905 9.15385C31.8988 9.92308 32.2029 11.0918 32.2029 12.6601H29.0007V11.5778C29.0007 11.3631 28.9381 11.1962 28.8129 11.0769C28.6936 10.9577 28.5297 10.898 28.321 10.898C28.0705 10.898 27.8857 10.9696 27.7664 11.1127C27.6531 11.2499 27.5964 11.4287 27.5964 11.6494C27.5964 11.87 27.6561 12.1085 27.7753 12.3649C27.8946 12.6213 28.1301 12.9165 28.482 13.2504L30.2977 14.9946C30.6614 15.3405 30.9954 15.7072 31.2995 16.0948C31.6036 16.4764 31.8481 16.9237 32.0329 17.4365C32.2178 17.9434 32.3102 18.5635 32.3102 19.297C32.3102 20.7758 32.0359 21.9356 31.4873 22.7764C30.9447 23.6112 29.9966 24.0286 28.643 24.0286Z" fill={color} fillOpacity={fillOpacity}/>
    <path d="M16.2458 23.8855V8.14311H19.3585V14.449L20.8254 8.14311H23.9918L22.2297 15.3524L24.3585 23.8855H21.0938L19.3764 16.2648V23.8855H16.2458Z" fill={color} fillOpacity={fillOpacity}/>
    <path d="M7.21183 23.8855V8.14311H11.541C12.674 8.14311 13.5267 8.45915 14.0991 9.09123C14.6716 9.71735 14.9578 10.6357 14.9578 11.8462V19.2165C14.9578 20.7072 14.6954 21.8581 14.1707 22.6691C13.6519 23.48 12.7396 23.8855 11.4337 23.8855H7.21183ZM10.3782 21.1038H10.9238C11.5022 21.1038 11.7914 20.8235 11.7914 20.263V12.1413C11.7914 11.6166 11.7199 11.2797 11.5768 11.1306C11.4396 10.9756 11.1564 10.898 10.727 10.898H10.3782V21.1038Z" fill={color} fillOpacity={fillOpacity}/>
    </svg>
  )
}

export default DksIcon;