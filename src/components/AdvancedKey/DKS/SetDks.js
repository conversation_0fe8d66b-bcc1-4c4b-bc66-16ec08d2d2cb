const SetDks = (hexArray, set<PERSON><PERSON><PERSON><PERSON><PERSON>er, update<PERSON><PERSON><PERSON>, setAdvanced<PERSON><PERSON>, advancedKey) => {
  const newItem = {
    keycap_row: hexArray[2],
    keycap_col: hexArray[3],
    press_trigger_point: `${hexArray[4]} ${hexArray[5]}`,
    release_trigger_point: `${hexArray[6]} ${hexArray[7]}`,
    keycap1_code: `${hexArray[8]} ${hexArray[9]}`,
    keycap1_config: hexArray[10],
    keycap2_code: `${hexArray[11]} ${hexArray[12]}`,
    keycap2_config: hexArray[13],
    keycap3_code: `${hexArray[14]} ${hexArray[15]}`,
    keycap3_config: hexArray[16],
    keycap4_code: `${hexArray[17]} ${hexArray[18]}`,
    keycap4_config: hexArray[19]
  };

  updateKeycap(newItem.keycap_row, newItem.keycap_col, { advancedKeyType: "dks" }, "00");

  setAdvancedKey(prevState => {
    // Check if item with same keys already exists
    const isDuplicate = prevState.dks.list.some(item =>
      item.keycap_row === newItem.keycap_row &&
      item.keycap_col === newItem.keycap_col
    );

    if (isDuplicate) {
      return prevState;
    }

    return {
      ...prevState,
      dks: {
        ...prevState.dks,
        list: [...prevState.dks.list, newItem]
      }
    };
  });
}

export default SetDks;