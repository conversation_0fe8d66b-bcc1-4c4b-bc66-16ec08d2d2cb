const SetMt = (hexArray, set<PERSON>urrent<PERSON><PERSON>er, update<PERSON>eycap, setAdvanced<PERSON>ey, advancedKey) => {
  const newItem = {
    keycap_row: hexArray[2],
    keycap_col: hexArray[3],
    long_press_time: `${hexArray[4]} ${hexArray[5]}`,
    short_press_code: `${hexArray[6]} ${hexArray[7]}`,
    long_press_code: `${hexArray[8]} ${hexArray[9]}`
  };

  updateKeycap(newItem.keycap_row, newItem.keycap_col, { advancedKeyType: "mt" }, "00");

  setAdvancedKey(prevState => {
    // Check if item with same keys already exists
    const isDuplicate = prevState.mt.list.some(item =>
      item.keycap_row === newItem.keycap_row &&
      item.keycap_col === newItem.keycap_col
    );

    if (isDuplicate) {
      return prevState;
    }

    return {
      ...prevState,
      mt: {
        ...prevState.mt,
        list: [...prevState.mt.list, newItem]
      }
    };
  });
}

export default SetMt;