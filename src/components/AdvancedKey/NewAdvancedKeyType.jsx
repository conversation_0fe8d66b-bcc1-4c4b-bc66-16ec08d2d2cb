import socdIcon from '../../assets/socd.svg';
import mtIcon from '../../assets/mt.svg';
import dksIcon from '../../assets/dks.svg';
import rsIcon from '../../assets/rs.svg';
import tglIcon from '../../assets/tgl.svg';
import { useTranslation } from 'react-i18next';

const NewAdvancedKeyType = ({currentAdvancedKeyType, onSelect, deviceProductId,stepNumber=0}) => {
  const { t } = useTranslation();
  if ([36880, 36869, 32773, 32784].includes(deviceProductId))
  {
    return (<>
      <div  className={`${stepNumber === 0 ? 'advanced-key-backgroud' : ''}`}>
        <div className="advanced-key-list">
          <div className={`advanced-key-list-item ${currentAdvancedKeyType === 'rs' ? 'active' : ''}`} onClick={() => onSelect('rs')}>
            <div className="img-container">
              <img src={rsIcon} alt="" />
              <span>{t('advanced_key.rs.title')}</span>
            </div>
            <div className="text">
              <div>{t('advanced_key.list.rs_tip')}</div>
            </div>
          </div>
          <div className={`advanced-key-list-item ${currentAdvancedKeyType === 'socd' ? 'active' : ''}`} onClick={() => onSelect('socd')}>
            <div className="img-container">
              <img src={socdIcon} alt="" />
              <span>{t('advanced_key.socd.title')}</span>
            </div>
            <div className="text">
              <div>{t('advanced_key.list.socd_tip')}</div>
            </div>
          </div>
        </div>
        <div className="advanced-key-list">
          <div className={`advanced-key-list-item ${currentAdvancedKeyType === 'mt' ? 'active' : ''}`} onClick={() => onSelect('mt')}>
            <div className="img-container">
              <img src={mtIcon} alt="" />
               <span>{t('advanced_key.mt.title')}</span>
            </div>
            <div className="text">
              <div>{t('advanced_key.list.mt_tip')}</div>
            </div>
          </div>
          <div className={`advanced-key-list-item ${currentAdvancedKeyType === 'dks' ? 'active' : ''}`} onClick={() => onSelect('dks')}>
            <div className="img-container">
              <img src={dksIcon} alt="" />
               <span>{t('advanced_key.dks.title')}</span>
            </div>
            <div className="text">
              <div>{t('advanced_key.list.dks_tip')}</div>
            </div>
          </div>
        </div>
      </div>
    </>)
  }else{
    return (
      <div  className={`${stepNumber === 0 ? 'advanced-key-backgroud' : ''}`}>
        <div className="advanced-key-list">
          <div className={`advanced-key-list-item ${currentAdvancedKeyType === 'rs' ? 'active' : ''}`} onClick={() => onSelect('rs')}>
            <div className="img-container">
              <img src={rsIcon} alt="" />
               <span>{t('advanced_key.rs.title')}</span>
            </div>
            <div className="text">
              <div>{t('advanced_key.list.rs_tip')}</div>
            </div>
          </div>
          <div className={`advanced-key-list-item ${currentAdvancedKeyType === 'socd' ? 'active' : ''}`} onClick={() => onSelect('socd')}>
            <div className="img-container">
              <img src={socdIcon} alt="" />
              <span>{t('advanced_key.socd.title')}</span>
            </div>
            <div className="text">
              <div>{t('advanced_key.list.socd_tip')}</div>
            </div>
          </div>
        </div>
        <div className="advanced-key-list">
          <div className={`advanced-key-list-item ${currentAdvancedKeyType === 'mt' ? 'active' : ''}`} onClick={() => onSelect('mt')}>
            <div className="img-container">
              <img src={mtIcon} alt="" />
              <span>{t('advanced_key.mt.title')}</span>
            </div>
            <div className="text">
              <div>{t('advanced_key.list.mt_tip')}</div>
            </div>
          </div>
          <div className={`advanced-key-list-item ${currentAdvancedKeyType === 'dks' ? 'active' : ''}`} onClick={() => onSelect('dks')}>
            <div className="img-container">
              <img src={dksIcon} alt="" />
              <span>{t('advanced_key.dks.title')}</span>
            </div>
            <div className="text">
              <div>{t('advanced_key.list.dks_tip')}</div>
            </div>
          </div>
        </div>
        <div className={`advanced-key-list-item ${currentAdvancedKeyType === 'tgl' ? 'active' : ''}`} onClick={() => onSelect('tgl')}>
          <div className="img-container">
            <img src={tglIcon} alt="" />
            <span>{t('advanced_key.tgl.title')}</span>
          </div>
          <div className="text">
            <div>{t('advanced_key.list.tgl_tip')}</div>
          </div>
        </div>
      </div>
    )
  }
};

export default NewAdvancedKeyType;