import { CloseOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useKeyboard } from '../../Keyboard/KeyboardContext';
import RsIcon from './RsIcon';
import processString from '../../Keyboard/processString' ;
const RsItem = ({ item, isActive, onSelect, onDelete }) => {
  const { data } = useKeyboard();
  const firstKey = data.keycaps[data.currentLayer]?.[`${item.keycap1_row}-${item.keycap1_col}`];
  const secondKey = data.keycaps[data.currentLayer]?.[`${item.keycap2_row}-${item.keycap2_col}`];

  return (
    <div className="advanced-key-item-wrapper" onClick={onSelect}>
      <div className={`d-flex justify-content-between align-items-center advanced-key-item ${isActive ? 'active' : ''}`}>
        <div className="d-flex align-items-center">
          <div className="d-flex align-items-center" style={{width: '80px', gap: '8px'}}>
            <div className="keycap1 keycap-item">
               {/* {firstKey?.label || ''} */}
                <svg aria-hidden="true"  width="36"  height="36">
                     <use href= {`#icon-${firstKey?.label  ?   processString(firstKey?.label,'picture') : 'evenodd'}`}/>
                </svg>
            </div>
            <div className="keycap2 keycap-item">
              {/* {secondKey?.label || ''} */}
               <svg aria-hidden="true"  width="36"  height="36">
                     <use href= {`#icon-${secondKey?.label ?   processString(secondKey?.label,'picture') : 'evenodd'}`}/>
                </svg>
            </div>
          </div>
        </div>
        <div className="d-flex align-items-center">
          <div className="icon">
            <RsIcon color={isActive ? "var(--bs-primary)" : "#EFF0F5"} fillOpacity={isActive ? "1" : "0.45"} />
          </div>
          <div style={{width: '36px'}}>
            <Button type="text" icon={<CloseOutlined />} className="delete-button" onClick={onDelete} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RsItem;