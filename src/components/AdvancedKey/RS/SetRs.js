const SetRs = (hex<PERSON>rray, set<PERSON><PERSON><PERSON><PERSON><PERSON>er, update<PERSON>eycap, setAdvanced<PERSON>ey, advancedKey) => {
  const newItem = {
    keycap1_row: hexArray[2],
    keycap1_col: hexArray[3],
    keycap2_row: hexArray[4],
    keycap2_col: hexArray[5]
  };

  updateKeycap(newItem.keycap1_row, newItem.keycap1_col, { advancedKeyType: "rs" }, "00");
  updateKeycap(newItem.keycap2_row, newItem.keycap2_col, { advancedKeyType: "rs" }, "00");

  setAdvancedKey(prevState => {
    // Check if item with same keys already exists
    const isDuplicate = prevState.rs.list.some(item =>
      (item.keycap1_row === newItem.keycap1_row &&
       item.keycap1_col === newItem.keycap1_col &&
       item.keycap2_row === newItem.keycap2_row &&
       item.keycap2_col === newItem.keycap2_col) ||
      (item.keycap1_row === newItem.keycap2_row &&
       item.keycap1_col === newItem.keycap2_col &&
       item.keycap2_row === newItem.keycap1_row &&
       item.keycap2_col === newItem.keycap1_col)
    );

    if (isDuplicate) {
      return prevState;
    }

    return {
      ...prevState,
      rs: {
        ...prevState.rs,
        list: [...prevState.rs.list, newItem]
      }
    };
  });
}

export default SetRs;