const SetSocd = (hex<PERSON>rray, set<PERSON>ur<PERSON><PERSON><PERSON>er, update<PERSON><PERSON><PERSON>, setAdvanced<PERSON>ey, advancedKey) => {
  const newItem = {
    keycap1_row: hexArray[2],
    keycap1_col: hexArray[3],
    keycap2_row: hexArray[4],
    keycap2_col: hexArray[5],
    mode: hexArray[6]
  };

  updateKeycap(newItem.keycap1_row, newItem.keycap1_col, { advancedKeyType: "socd" }, "00");
  updateKeycap(newItem.keycap2_row, newItem.keycap2_col, { advancedKeyType: "socd" }, "00");

  setAdvancedKey(prevState => {
    // Check if item with same keys already exists
    const isDuplicate = prevState.socd.list.some(item =>
      (item.keycap1_row === newItem.keycap1_row &&
       item.keycap1_col === newItem.keycap1_col &&
       item.keycap2_row === newItem.keycap2_row &&
       item.keycap2_col === newItem.keycap2_col) ||
      (item.keycap1_row === newItem.keycap2_row &&
       item.keycap1_col === newItem.keycap2_col &&
       item.keycap2_row === newItem.keycap1_row &&
       item.keycap2_col === newItem.keycap1_col)
    );

    if (isDuplicate) {
      return prevState;
    }

    return {
      ...prevState,
      socd: {
        ...prevState.socd,
        list: [...prevState.socd.list, newItem]
      }
    };
  });
}

export default SetSocd;