import React from 'react';
import { useHandleDevice } from '../../HIDDevice/HandleDeviceContext';
import { useKeyboard } from '../../Keyboard/KeyboardContext';
import { useState, useEffect } from 'react';
import { Button } from 'antd';
import { parseHex, changeToHex } from '../../../utils/hidUtils';
import { useTranslation } from 'react-i18next';
import processString from '../../Keyboard/processString' ;
import { Switch } from 'antd';


const SocdConfig = ({
  firstKeyRow,
  firstKeyCol,
  secondKeyRow,
  secondKeyCol,
  selectedMode,
  handleSave,
  stepNumber
}) => {
  const { t } = useTranslation();
  const { advancedKey, setAdvancedKey, addToQueue } = useHandleDevice();
  const { data, setCurrentSelectedKeycaps, updateKeycap } = useKeyboard();
  const firstKey = data.keycaps[data.currentLayer]?.[`${firstKeyRow}-${firstKeyCol}`];
  const secondKey = data.keycaps[data.currentLayer]?.[`${secondKeyRow}-${secondKeyCol}`];
  const [activeKeyInput, setActiveKeyInput] = useState(1);
  let firstKeyInput = {
    row: firstKeyRow || 0,
    col: firstKeyCol || 0
  };
  let secondKeyInput = {
    row: secondKeyRow || 0,
    col: secondKeyCol || 0
  };

  const modes = [
    { id: '00', text: t('advanced_key.socd.back_cover') },
    { id: '01', text: t('advanced_key.socd.mutual_exclusion') },
    { id: '02', text: `${t('advanced_key.socd.priority')} ${firstKey?.label || t('advanced_key.socd.key_1')} ` },
    { id: '03', text: `${t('advanced_key.socd.priority')} ${secondKey?.label || t('advanced_key.socd.key_2')} ` }
  ];

  const SelectedKeycapsSync = () => {
    const newList = [...advancedKey.socd.list];
    const currentItem = newList[advancedKey.selectedIndex];
    if (currentItem) {
      if (data.currentSelectedKeycaps.length === 1) {
        if (secondKeyInput.row === data.currentSelectedKeycaps[0].row && secondKeyInput.col === data.currentSelectedKeycaps[0].column) {
          firstKeyInput = { row: 0, col: 0 };
          secondKeyInput = { row: data.currentSelectedKeycaps[0].row, col: data.currentSelectedKeycaps[0].column };
        } else {
          firstKeyInput = { row: data.currentSelectedKeycaps[0].row, col: data.currentSelectedKeycaps[0].column };
          secondKeyInput = { row: 0, col: 0 };
        }
        // Compare with firstKeyInput and secondKeyInput to determine which one was deselected
        if (firstKeyInput.row === data.currentSelectedKeycaps[0].row && firstKeyInput.col === data.currentSelectedKeycaps[0].column) {
          // First key remains selected, reset second key
          firstKeyInput = { row: data.currentSelectedKeycaps[0].row, col: data.currentSelectedKeycaps[0].column };
          secondKeyInput = { row: 0, col: 0 };
          currentItem.keycap1_row = data.currentSelectedKeycaps[0].row;
          currentItem.keycap1_col = data.currentSelectedKeycaps[0].column;
          currentItem.keycap2_row = 0;
          currentItem.keycap2_col = 0;
          setActiveKeyInput(2)
        } else if (secondKeyInput.row === data.currentSelectedKeycaps[0].row && secondKeyInput.col === data.currentSelectedKeycaps[0].column) {
          // Second key remains selected, reset first key
          firstKeyInput = { row: 0, col: 0 };
          secondKeyInput = { row: data.currentSelectedKeycaps[0].row, col: data.currentSelectedKeycaps[0].column };
          currentItem.keycap1_row = 0;
          currentItem.keycap1_col = 0;
          currentItem.keycap2_row = data.currentSelectedKeycaps[0].row;
          currentItem.keycap2_col = data.currentSelectedKeycaps[0].column;
          setActiveKeyInput(1)
        }
      } else if (data.currentSelectedKeycaps.length === 2) {
        if (data.currentSelectedKeycaps[0].label != undefined && data.currentSelectedKeycaps[1].label != undefined) {
          if (secondKeyInput.row === data.currentSelectedKeycaps[0].row && secondKeyInput.col === data.currentSelectedKeycaps[0].column) {
            firstKeyInput = { row: data.currentSelectedKeycaps[1].row, col: data.currentSelectedKeycaps[1].column };
            secondKeyInput = { row: data.currentSelectedKeycaps[0].row, col: data.currentSelectedKeycaps[0].column };
            currentItem.keycap1_row = data.currentSelectedKeycaps[1].row;
            currentItem.keycap1_col = data.currentSelectedKeycaps[1].column;
            currentItem.keycap2_row = data.currentSelectedKeycaps[0].row;
            currentItem.keycap2_col = data.currentSelectedKeycaps[0].column;
          } else {
            firstKeyInput = { row: data.currentSelectedKeycaps[0].row, col: data.currentSelectedKeycaps[0].column };
            secondKeyInput = { row: data.currentSelectedKeycaps[1].row, col: data.currentSelectedKeycaps[1].column };
            currentItem.keycap1_row = data.currentSelectedKeycaps[0].row;
            currentItem.keycap1_col = data.currentSelectedKeycaps[0].column;
            currentItem.keycap2_row = data.currentSelectedKeycaps[1].row;
            currentItem.keycap2_col = data.currentSelectedKeycaps[1].column;
          }
          setActiveKeyInput(2)
        } else {
          setCurrentSelectedKeycaps([])
        }
      } else {
        firstKeyInput = { row: 0, col: 0 };
        secondKeyInput = { row: 0, col: 0 };
        currentItem.keycap1_row = 0;
        currentItem.keycap1_col = 0;
        currentItem.keycap2_row = 0;
        currentItem.keycap2_col = 0;
        setActiveKeyInput(1)
      }
    }

    setAdvancedKey({
      ...advancedKey,
      socd: {
        ...advancedKey.socd,
        list: newList
      }
    });
  }

  const socdKeyClick = (keyNumber) => {
    if (keyNumber === 1) {
      setActiveKeyInput(1)
      if (data.currentSelectedKeycaps.length === 1) {
        setCurrentSelectedKeycaps([])
      } else {
        setCurrentSelectedKeycaps([{ row: secondKeyInput.row, column: secondKeyInput.col, label: secondKey?.label }])
      }

    } else if (keyNumber === 2) {
      setActiveKeyInput(2)
      if (data.currentSelectedKeycaps.length === 1) {
        setCurrentSelectedKeycaps([])
      } else {
        setCurrentSelectedKeycaps([{ row: firstKeyInput.row, column: firstKeyInput.col, label: firstKey?.label }])
      }
    }
  };

  useEffect(() => {
    SelectedKeycapsSync()
  }, [data.currentSelectedKeycaps]);

  const handleModeSelect = (mode) => {
    const newList = [...advancedKey.socd.list];
    newList[advancedKey.selectedIndex] = {
      ...newList[advancedKey.selectedIndex],
      mode
    };
    setAdvancedKey({
      ...advancedKey,
      socd: {
        ...advancedKey.socd,
        list: newList
      }
    });
  };

  return (<>
  <div className="socd-config">
    {stepNumber === 2 && <>
      <div className="rs-config-content">
        <div className='advanced_keyrs_title'><span>{t('advanced_key.socd.advanced_keyrs_title')}</span></div>
        <div className='advanced_keyrs_content'><span>{t('advanced_key.socd.advanced_keyrs_content')}</span></div>
      </div>
      <div className="rs-config-item">
        <div className={`rs-config-key-container ${activeKeyInput === 1 ? 'active' : ''}`}  onClick={() =>  socdKeyClick(1)}>
          <div className="rs-config-key-choose">
             <div className="rs-config-key-choose-icon">
                <svg aria-hidden="true"  width="48"  height="48">
                  <use href= {`#icon-${firstKey?.label  ?   processString(firstKey?.label,'picture') : 'evenodd'}`}/>
                </svg>
             </div>
          </div>
          <div className="rs-config-key-change">
            {
              firstKey?.label && <div className='rs-config-key-change-check' >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8Z" fill="var(--bs-primary)"/>
                  <path d="M5.40078 11.1422L11.7428 4.80021L12.7998 5.85721L6.45778 12.1992L5.40078 11.1422Z" fill="white"/>
                  <path d="M4.20781 7.78125L7.31062 10.8841L6.25362 11.941L3.15082 8.83825L4.20781 7.78125Z" fill="white"/>
              </svg>
            </div>
            }
            <div className="rs-config-key-change-text">
            <span>{t('advanced_key.socd.key_1')}</span>
            </div>
          </div>
        </div>
        <div className={`rs-config-key-container ${activeKeyInput === 2 ? 'active' : ''}`} onClick={() => socdKeyClick(2)}>
          <div className="rs-config-key-choose">
             <div className="rs-config-key-choose-icon">
               <svg aria-hidden="true"  width="48"  height="48">
                  <use href= {`#icon-${secondKey?.label  ?   processString(secondKey?.label,'picture') : 'evenodd'}`}/>
                </svg>
             </div>
          </div>
          <div className="rs-config-key-change">
          {
              secondKey?.label && <div className='rs-config-key-change-check' >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8Z" fill="var(--bs-primary)"/>
                  <path d="M5.40078 11.1422L11.7428 4.80021L12.7998 5.85721L6.45778 12.1992L5.40078 11.1422Z" fill="white"/>
                  <path d="M4.20781 7.78125L7.31062 10.8841L6.25362 11.941L3.15082 8.83825L4.20781 7.78125Z" fill="white"/>
              </svg>
            </div>
            }
            <div className="rs-config-key-change-text">
               <span>{ t('advanced_key.socd.key_2')}</span>
            </div>
          </div>
        </div>
      </div>
    </>}
    {stepNumber === 3 && <>
       <div className='socd-config-modes'>
         <div className="rs-config-content">
           <div className='advanced_keyrs_title'><span>{t('advanced_key.socd.advanced_socdmodes_title')}</span></div>
           <div className='advanced_keyrs_content'><span>{t('advanced_key.socd.advanced_socdmodes_content')}</span></div>
         </div>
        <div className="socd-config-modes-items">
           <div className="socd-config-modes-items-cards">
           {modes.map(mode => (
            <div key={`modes-items-card${mode.id}`} className={`modes-items-card ${selectedMode === mode.id ? 'active' : ''}`} onClick={() => handleModeSelect(mode.id)}>
                 <div className='modes-item-text'><span>{mode.text}</span></div>
                 <div className='modes-item-checked'>
                    {
                      selectedMode === mode.id &&
                         <div className='modes-item-checked-icon'>
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                               <path d="M20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10Z" fill="white"/>
                               <path d="M6.75093 13.9275L14.6784 6L15.9996 7.32124L8.07218 15.2487L6.75093 13.9275Z" fill="var(--bs-primary)"/>
                               <path d="M5.25972 9.7263L9.13823 13.6048L7.81698 14.926L3.93848 11.0475L5.25972 9.7263Z" fill="var(--bs-primary)"/>
                           </svg>
                        </div>
                    }
                 </div>
              </div>
           ))}
           </div>
           <div className="socd-config-alert alert_fill-4">
              <div className='socd-config-content'>
                  <div className='socd-config-icon'>
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                      <path d="M7 0C3.13438 0 0 3.13438 0 7C0 10.8656 3.13438 14 7 14C10.8656 14 14 10.8656 14 7C14 3.13438 10.8656 0 7 0ZM7.5 10.375C7.5 10.4438 7.44375 10.5 7.375 10.5H6.625C6.55625 10.5 6.5 10.4438 6.5 10.375V6.125C6.5 6.05625 6.55625 6 6.625 6H7.375C7.44375 6 7.5 6.05625 7.5 6.125V10.375ZM7 5C6.80374 4.99599 6.61687 4.91522 6.47948 4.775C6.3421 4.63478 6.26515 4.4463 6.26515 4.25C6.26515 4.0537 6.3421 3.86522 6.47948 3.725C6.61687 3.58478 6.80374 3.50401 7 3.5C7.19626 3.50401 7.38313 3.58478 7.52052 3.725C7.6579 3.86522 7.73485 4.0537 7.73485 4.25C7.73485 4.4463 7.6579 4.63478 7.52052 4.775C7.38313 4.91522 7.19626 4.99599 7 5Z" fill="#D7D7DB" fillOpacity="0.4"/>
                    </svg>
                  </div>
                  <span className='text-3'>{t('advanced_key.socd.advanced_socdmodes_alert')}</span>
              </div>
           </div>
        </div>
       </div>
       {/* <div className='socd-config-modes-bottom'>
           <div className='modes-bottom-items'>
              <div className="rs-config-content">
                <div className='advanced_keyrs_title'>
                  <span class='advanced_socdmodes_title'>{t('advanced_key.socd.advanced_socdmodes_title')}</span>
                  <Switch checked={true} onChange={() => {}} />
                </div>
                <div className='advanced_keyrs_content'><span>{t('advanced_key.socd.advanced_socdmodes_content')}</span></div>
              </div>
           </div>
       </div> */}
    </>}
    </div>

  </>
  );
};

export default SocdConfig;