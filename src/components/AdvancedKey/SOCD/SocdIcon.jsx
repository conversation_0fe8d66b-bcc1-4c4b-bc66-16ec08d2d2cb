const SocdIcon = ({ color, fillOpacity }) => {
  return (
    <svg width="37" height="36" viewBox="0 0 37 36" fill={color} xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_6267_6616)">
      <path d="M1.51416 27.0877H36.6125V28H1.51416V27.0877Z" fill={color} fillOpacity={fillOpacity}/>
      <path d="M28.2762 23.8855V8.14311H32.6054C33.7384 8.14311 34.5911 8.45915 35.1635 9.09123C35.736 9.71735 36.0222 10.6357 36.0222 11.8462V19.2165C36.0222 20.7072 35.7598 21.8581 35.2351 22.6691C34.7163 23.48 33.804 23.8855 32.4981 23.8855H28.2762ZM31.4426 21.1038H31.9882C32.5666 21.1038 32.8558 20.8235 32.8558 20.263V12.1413C32.8558 11.6166 32.7843 11.2797 32.6412 11.1306C32.504 10.9756 32.2208 10.898 31.7914 10.898H31.4426V21.1038Z" fill={color} fillOpacity={fillOpacity}/>
      <path d="M23.2494 24.0286C22.1105 24.0286 21.1922 23.6738 20.4945 22.9642C19.8028 22.2546 19.4569 21.2618 19.4569 19.9857V12.7227C19.4569 11.1962 19.758 10.0274 20.3603 9.21646C20.9685 8.40549 21.9554 8 23.321 8C24.0663 8 24.7282 8.13715 25.3066 8.41145C25.891 8.68575 26.3502 9.0972 26.6841 9.6458C27.018 10.1884 27.185 10.8742 27.185 11.703V14.4222H24.0544V12.0966C24.0544 11.6255 23.9948 11.3095 23.8755 11.1485C23.7563 10.9815 23.5714 10.898 23.321 10.898C23.0288 10.898 22.832 11.0054 22.7306 11.22C22.6292 11.4287 22.5786 11.709 22.5786 12.0608V19.941C22.5786 20.3763 22.6412 20.6834 22.7664 20.8623C22.8976 21.0411 23.0824 21.1306 23.321 21.1306C23.5893 21.1306 23.7771 21.0203 23.8845 20.7996C23.9978 20.579 24.0544 20.2928 24.0544 19.941V17.1055H27.2208V20.0841C27.2208 21.4675 26.8719 22.4723 26.1743 23.0984C25.4766 23.7185 24.5016 24.0286 23.2494 24.0286Z" fill={color} fillOpacity={fillOpacity}/>
      <path d="M14.4301 24.0286C13.1719 24.0286 12.2089 23.65 11.541 22.8927C10.8791 22.1294 10.5481 21.0292 10.5481 19.5921V12.1145C10.5481 10.7668 10.8761 9.74419 11.532 9.04651C12.1939 8.34884 13.16 8 14.4301 8C15.7002 8 16.6632 8.34884 17.3192 9.04651C17.9811 9.74419 18.312 10.7668 18.312 12.1145V19.5921C18.312 21.0292 17.9781 22.1294 17.3102 22.8927C16.6483 23.65 15.6883 24.0286 14.4301 24.0286ZM14.4569 21.1306C14.934 21.1306 15.1725 20.6685 15.1725 19.7442V12.0608C15.1725 11.2856 14.9399 10.898 14.4748 10.898C13.9501 10.898 13.6877 11.2946 13.6877 12.0877V19.7621C13.6877 20.251 13.7473 20.6029 13.8666 20.8175C13.9858 21.0262 14.1826 21.1306 14.4569 21.1306Z" fill={color} fillOpacity={fillOpacity}/>
      <path d="M5.95065 24.0286C4.51356 24.0286 3.476 23.6708 2.83795 22.9553C2.20587 22.2397 1.88983 21.1008 1.88983 19.5385V18H5.00253V19.9678C5.00253 20.3315 5.0562 20.6178 5.16353 20.8265C5.27683 21.0292 5.47063 21.1306 5.74493 21.1306C6.03115 21.1306 6.22793 21.0471 6.33527 20.8801C6.44857 20.7132 6.50522 20.4389 6.50522 20.0572C6.50522 19.5742 6.45751 19.1717 6.3621 18.8497C6.26669 18.5218 6.09973 18.2117 5.86121 17.9195C5.62865 17.6213 5.30367 17.2755 4.88625 16.8819L3.47302 15.5403C2.41756 14.5444 1.88983 13.4055 1.88983 12.1234C1.88983 10.7818 2.19991 9.75909 2.82006 9.05546C3.44618 8.35182 4.34958 8 5.53026 8C6.97331 8 7.99597 8.38462 8.59824 9.15385C9.20647 9.92308 9.51058 11.0918 9.51058 12.6601H6.30844V11.5778C6.30844 11.3631 6.24582 11.1962 6.1206 11.0769C6.00134 10.9577 5.83736 10.898 5.62865 10.898C5.3782 10.898 5.19335 10.9696 5.07409 11.1127C4.96079 11.2499 4.90414 11.4287 4.90414 11.6494C4.90414 11.87 4.96377 12.1085 5.08303 12.3649C5.20229 12.6213 5.43783 12.9165 5.78965 13.2504L7.60539 14.9946C7.96914 15.3405 8.30307 15.7072 8.60718 16.0948C8.9113 16.4764 9.15578 16.9237 9.34064 17.4365C9.52549 17.9434 9.61792 18.5635 9.61792 19.297C9.61792 20.7758 9.34362 21.9356 8.79502 22.7764C8.25238 23.6112 7.30426 24.0286 5.95065 24.0286Z" fill={color} fillOpacity={fillOpacity}/>
      </g>
      <defs>
      <clipPath id="clip0_6267_6616">
      <rect width="36" height="36" fill="white" transform="translate(0.5)"/>
      </clipPath>
      </defs>
    </svg>
  )
}

export default SocdIcon;