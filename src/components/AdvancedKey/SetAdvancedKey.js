import SetSocd from './SOCD/SetSocd';
import SetMt from './MT/SetMt';
import SetDks from './DKS/SetDks';
import SetRs from './RS/SetRs';
import SetTgl from './TGL/SetTgl';

const SetAdvancedKey = (hexArray, setCurrentLayer, updateKeycap, setAdvancedKey, advancedKey, keyboardData) => {
  if (hexArray[0] === '20') {
    SetSocd(hexArray, setCurrentLayer, updateKeycap, setAdvancedKey, advancedKey);
  } else if (hexArray[0] === '24') {
    SetMt(hexArray, setCurrentLayer, updateKeycap, setAdvancedKey, advancedKey);
  } else if (hexArray[0] === '2C') {
    SetDks(hexArray, setCurrentLayer, updateKeycap, setAdvancedKey, advancedKey);
  } else if (hexArray[0] === '30') {
    SetRs(hexArray, setCurrentLayer, updateKeycap, setAdvancedKey, advancedKey);
  } else if (hexArray[0] === '28') {
    SetTgl(hexArray, setCurrentLayer, updateKeycap, setAdvancedKey, advancedKey);
  }
}

export default SetAdvancedKey;
