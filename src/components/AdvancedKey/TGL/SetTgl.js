const SetTgl = (hex<PERSON>rray, set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, updateKeycap, setAdvancedKey, advancedKey) => {
  const newItem = {
    keycap_row: hexArray[2],
    keycap_col: hexArray[3],
    keycode: `${hexArray[4]} ${hexArray[5]}`,
  };

  updateKeycap(newItem.keycap_row, newItem.keycap_col, { advancedKeyType: "tgl" }, "00");

  setAdvancedKey(prevState => {
    // Check if item with same keys already exists
    const isDuplicate = prevState.tgl.list.some(item =>
      item.keycap_row === newItem.keycap_row &&
      item.keycap_col === newItem.keycap_col
    );

    if (isDuplicate) {
      return prevState;
    }

    return {
      ...prevState,
      tgl: {
        ...prevState.tgl,
        list: [...prevState.tgl.list, newItem]
      }
    };
  });
}

export default SetTgl;