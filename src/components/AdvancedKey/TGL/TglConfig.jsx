import { <PERSON><PERSON><PERSON>, Mo<PERSON> } from 'antd';
import { useState, useEffect } from 'react';
import { useHandleDevice } from '../../HIDDevice/HandleDeviceContext';
import { useKeyboard } from '../../Keyboard/KeyboardContext';
import Keymap from '../../Keymap';
import { findCodeByKey, findNameByCode, parseHex, changeToHighLowHex } from '../../../utils/hidUtils';
import { useTranslation } from 'react-i18next';
import processString from '../../Keyboard/processString' ;
const TglConfig = ({currentStep}) => {
  const [activeKey, setActiveKey] = useState('current');
  const { advancedKey, setAdvancedKey, addToQueue } = useHandleDevice();
  const { data, setCurrentSelectedKeycaps, updateKeycap } = useKeyboard();
  const [open, setOpen] = useState(false);
  const currentKey = data.keycaps[data.currentLayer]?.[`${advancedKey.tgl.list[advancedKey.selectedIndex]?.keycap_row}-${advancedKey.tgl.list[advancedKey.selectedIndex]?.keycap_col}`];
  const newList = [...advancedKey.tgl.list];
  const currentItem = newList[advancedKey.selectedIndex];
  const { t } = useTranslation();
  const handleKeyClick = (key) => {
    setActiveKey(key);
    if (key === 'hold') {
      setOpen(true)
    }
  }

  useEffect(() => {
    if (currentItem) {
      currentItem.keycap_row = data.currentSelectedKeycaps[0]?.row || 0;
      currentItem.keycap_col = data.currentSelectedKeycaps[0]?.column || 0;
    }
    setAdvancedKey(prev => ({
      ...prev,
      tgl: {
        ...prev.tgl,
        list: newList
      }
    }))
  }, [data.currentSelectedKeycaps]);

  const handleKeycapClick = (keycapName) => {
    currentItem.keycode = findCodeByKey(keycapName)
    setAdvancedKey(prev => ({
      ...prev,
      tgl: {
        ...prev.tgl,
        list: newList
      }
    }))
    setOpen(false)
  };

  return (
    <><div className="dks-config">
    {currentItem && <div>
      <Modal
        // title={t('advanced_key.mt.choose_keyboard')}
        centered
        open={open}
        onOk={() => setOpen(false)}
        className={'keybordmodal'}
        onCancel={() => setOpen(false)}
        width={1450}
      >
        <Keymap handleKeycapClick={handleKeycapClick} />
      </Modal>
      <div className="rs-config-content">
        <div className='advanced_keyrs_title'><span>{t('advanced_key.tgl.advanced_keyrs_title')}</span></div>
        <div className='advanced_keyrs_content'><span>{t('advanced_key.tgl.advanced_keyrs_content')}</span></div>
      </div>

      <div className="rs-config-item">
        <div className={`rs-config-key-container ${activeKey === 'current' ? 'active' : ''} `} onClick={() => handleKeyClick('current')}>
          <div className="rs-config-key-choose">
             <div className="rs-config-key-choose-icon">
                <svg aria-hidden="true"  width="48"  height="48">
                  <use href= {`#icon-${currentKey?.label  ?   processString(currentKey?.label,'picture') : 'evenodd'}`}/>
                </svg>
             </div>
          </div>
          <div className="rs-config-key-change">
            {
              currentKey?.label && <div className='rs-config-key-change-check' >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8Z" fill="var(--bs-primary)"/>
                  <path d="M5.40078 11.1422L11.7428 4.80021L12.7998 5.85721L6.45778 12.1992L5.40078 11.1422Z" fill="white"/>
                  <path d="M4.20781 7.78125L7.31062 10.8841L6.25362 11.941L3.15082 8.83825L4.20781 7.78125Z" fill="white"/>
              </svg>
            </div>
            }
            <div className="rs-config-key-change-text">
            <span>{ t('advanced_key.dks.key_1')}</span>
            </div>
          </div>
        </div>
        <div className={`dks-config-items-main-item `}  >
               <div className='dks-config-items-main-item-text'><span>{t('advanced_key.dks.key_1')}</span></div>
               <div className={`dks-config-items-main-item-key-container ${activeKey === 'hold' ? 'active' : ''} `} onClick={() => handleKeyClick('hold')}>
                  <svg aria-hidden="true"  width="36"  height="36">
                     <use href= {`#icon-${currentItem.keycode  ?   processString(findNameByCode(currentItem.keycode),'picture') : 'evenodd'}`}/>
                  </svg>
               </div>
        </div>

      </div>

    </div>}

   </div>
  </>)
}

export default TglConfig;