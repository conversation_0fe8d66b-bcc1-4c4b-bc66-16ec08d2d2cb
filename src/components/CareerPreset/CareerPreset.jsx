import { useTranslation } from 'react-i18next';
import { Avatar, Card, Button, Tabs, message } from 'antd';
const { Meta } = Card;
import fnc_img from '../../assets/career/fnc.jpg';
import lev_img from '../../assets/career/lev.jpg';
import nrg_img from '../../assets/career/nrg.jpg';
import prx_img from '../../assets/career/prx.jpg';
import tarik_img from '../../assets/career/tarik.jpg';
import tenz_img from '../../assets/career/tenz.jpg';
import fnc_dark_img from '../../assets/career/fnc_dark.jpg';
import lev_dark_img from '../../assets/career/lev_dark.jpg';
import nrg_dark_img from '../../assets/career/nrg_dark.jpg';
import prx_dark_img from '../../assets/career/prx_dark.jpg';
import tarik_dark_img from '../../assets/career/tarik_dark.jpg';
import tenz_dark_img from '../../assets/career/tenz_dark.jpg';

import { useState, useEffect } from 'react';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { useKeyboard } from '../Keyboard/KeyboardContext';
import { changeToHex, parseHex, changeToHighLowHex } from '../../utils/hidUtils';

const CareerPreset = () => {
  const { t, i18n } = useTranslation();
  const [selectedCard, setSelectedCard] = useState(null);
  const [hoveredCard, setHoveredCard] = useState(null);
  const [externalPresets, setExternalPresets] = useState([]);
  const { addToQueue, setKeyboardLoading, setFullScreenLoading, setFullScreenPercent, setFullScreenLoadingText, language, setLanguage, deviceProductId } = useHandleDevice();
  const { data, updateKeycap } = useKeyboard();
  const [messageApi, contextHolder] = message.useMessage();

  // 内置预设配置
  const defaultPresets = [
    {
      img: (i18n.language === "zh-CN") ? lev_img : lev_dark_img,
      title: '',
      desc: t('career_preset.career_info.lev_desc'),
      settings: {
        "01-02": ["01", 0.5, 0.1, 0.1, 0.2],
        "02-01": ["01", 0.5, 0.1, 0.1, 0.2],
        "02-02": ["01", 0.5, 0.1, 0.1, 0.2],
        "02-03": ["01", 0.5, 0.1, 0.1, 0.2],
        "01-00": ["00", 2.7, 0, 0, 0.2],
        "02-00": ["00", 2.7, 0, 0, 0.2],
        "02-05": ["00", 2.7, 0, 0, 0.2],
        "03-00": ["00", 2.0, 0, 0, 0.2],
        "04-00": ["00", 2.0, 0, 0, 0.2],
        "04-02": ["00", 2.0, 0, 0, 0.2],
        "03-04": ["00", 2.0, 0, 0, 0.2],
        "04-01": ["00", 3.3, 0, 0, 0.2],
        "04-04": ["00", 3.3, 0, 0, 0.2],
        "02-07": ["00", 2.2, 0, 0, 0.2],
        "03-01": ["00", 2.2, 0, 0, 0.2],
        "03-02": ["00", 2.2, 0, 0, 0.2],
        "03-03": ["00", 2.2, 0, 0, 0.2],
        "01-04": ["00", 2.2, 0, 0, 0.2],
        "01-05": ["00", 2.2, 0, 0, 0.2],
        "00-00": ["00", 3.3, 0, 0, 0.2],
        "other": ["00", 1, 0, 0, 0.2]
      }
    },
    {
      img: (i18n.language === "zh-CN") ? prx_img : prx_dark_img,
      title: '',
      desc: t('career_preset.career_info.prx_desc'),
      settings: {
        "01-02": ["01", 0.1, 0.15, 0.15, 0.2],
        "02-01": ["01", 0.1, 0.15, 0.15, 0.2],
        "02-02": ["01", 0.1, 0.15, 0.15, 0.2],
        "02-03": ["01", 0.1, 0.15, 0.15, 0.2],
        "04-00": ["01", 0.1, 0.15, 0.15, 0.2],
        "03-00": ["00", 1, 0, 0, 0.2],
        "other": ["00", 2, 0, 0, 0.2]
      }
    },
    {
      img: (i18n.language === "zh-CN") ? tenz_img : tenz_dark_img,
      title: '',
      desc: t('career_preset.career_info.tenz_desc'),
      settings: {
        "01-02": ["01", 0.1, 0.15, 0.15, 0.2],
        "02-01": ["01", 0.1, 0.15, 0.15, 0.2],
        "02-02": ["01", 0.1, 0.15, 0.15, 0.2],
        "02-03": ["01", 0.1, 0.15, 0.15, 0.2],
        "04-00": ["01", 0.1, 0.15, 0.15, 0.2],
        "04-03": ["00", 3, 0, 0, 0.2],
        "other": ["00", 1.5, 0, 0, 0.2]
      }
    },
    {
      img: (i18n.language === "zh-CN") ? nrg_img : nrg_dark_img,
      title: '',
      desc: t('career_preset.career_info.nrg_desc'),
      settings: {
        "01-02": ["01", 0.1, 0.15, 0.15, 0.2],
        "02-01": ["01", 0.1, 0.15, 0.15, 0.2],
        "02-02": ["01", 0.1, 0.15, 0.15, 0.2],
        "02-03": ["01", 0.1, 0.15, 0.15, 0.2],
        "04-03": ["00", 2.5, 0, 0, 0.2],
        "other": ["00", 1, 0, 0, 0.2]
      }
    },
    {
      img: (i18n.language === "zh-CN") ? tarik_img : tarik_dark_img,
      title: '',
      desc: t('career_preset.career_info.tarik_desc'),
      settings: {
        "01-02": ["01", 0.1, 0.5, 0.45, 0.2],
        "02-01": ["01", 0.1, 0.5, 0.45, 0.2],
        "02-02": ["01", 0.1, 0.5, 0.45, 0.2],
        "02-03": ["01", 0.1, 0.5, 0.45, 0.2],
        "other": ["00", 1, 0, 0, 0.2]
      }
    },
    {
      img: (i18n.language === "zh-CN") ? fnc_img : fnc_dark_img,
      title: '',
      desc: t('career_preset.career_info.fnc_desc'),
      settings: {
        "01-02": ["01", 0.2, 0.5, 0.5, 0.2],
        "02-01": ["01", 0.2, 0.5, 0.5, 0.2],
        "02-02": ["01", 0.2, 0.5, 0.5, 0.2],
        "02-03": ["01", 0.2, 0.5, 0.5, 0.2],
        "other": ["00", 1, 0, 0, 0.2]
      }
    }
  ];

  useEffect(() => {
    // 获取外部职业预设配置（开发环境不获取）
    if (import.meta.env.VITE_APP_ENV === 'Dev') return;

    const fetchExternalPresets = async () => {
      try {
        const response = await fetch(`${import.meta.env.VITE_API_URL}/api/career_presets/career_presets?deploy_env=${import.meta.env.VITE_API_ENV}`);
        const data = await response.json();
        setExternalPresets(data.data);
      } catch (error) {
        console.error('Failed to fetch external presets:', error);
        messageApi.error('Fetch fail');
      }
    };

    fetchExternalPresets();
  }, []);

  const updateKeyPerformance = (key_settings) => {
    // 配列
    const keys = Object.keys(data.keycaps["performance"]);
    const is80Layout = (deviceProductId === 9010 || deviceProductId === 29952); // 判断是否为80配列键盘
    // 如果是80配列键盘，调整所有按键的行号（除了ESC键）
    if (is80Layout) {
      const adjustedSettings = {};
      for (const [key, value] of Object.entries(key_settings)) {
        if (key === "other") {
          adjustedSettings[key] = value;
          continue;
        }
        const [rowHex, colHex] = key.split('-');
        if (rowHex === "00" && colHex === "00") {
          adjustedSettings[key] = value;
        } else {
          const rowDecimal = parseInt(rowHex, 16);
          const adjustedRowHex = (rowDecimal + 1).toString(16).padStart(2, '0');
          adjustedSettings[`${adjustedRowHex}-${colHex}`] = value;
        }
      }
      key_settings = adjustedSettings;
    }

    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const [rowHex, colHex] = key.split('-');
      let switchType = "";
      switchType = data.keycaps["performance"][`${rowHex}-${colHex}`]?.rapidTrigger?.switchType || "00";
      if (key_settings[`${rowHex}-${colHex}`]) {
        const mode = key_settings[`${rowHex}-${colHex}`][0];
        const triggerPoint = changeToHighLowHex(key_settings[`${rowHex}-${colHex}`][1] * 100);
        const pressTrigger = changeToHighLowHex(key_settings[`${rowHex}-${colHex}`][2] * 100);
        const releaseTrigger = changeToHighLowHex(key_settings[`${rowHex}-${colHex}`][3] * 100);
        const bottomProtection = changeToHighLowHex(key_settings[`${rowHex}-${colHex}`][4] * 100);
        let label;
        if (mode === '00') {
          label = (parseHex(triggerPoint) / 100.00).toFixed(2);
        } else {
          label = `${(parseHex(pressTrigger) / 100.00).toFixed(2)}<br/>${(parseHex(releaseTrigger) / 100.00).toFixed(2)}`;
        }
        addToQueue(`19 ${rowHex} ${colHex} ${mode} 00 00 ${triggerPoint} ${pressTrigger} ${releaseTrigger} ${bottomProtection} ${switchType} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
        updateKeycap(rowHex, colHex, {
          ...data.keycaps["performance"][`${rowHex}-${colHex}`],
          label,
          rapidTrigger: {
            ...data.keycaps["performance"][`${rowHex}-${colHex}`]?.rapidTrigger,
            triggerPoint: parseHex(triggerPoint) / 100.00,
            pressTravel: parseHex(pressTrigger) / 100.00,
            releaseStroke: parseHex(releaseTrigger) / 100.00,
            bottomProtectionTravel: parseHex(bottomProtection) / 100.00
          }
        }, "performance");
      } else {
        const mode = key_settings["other"][0];
        const triggerPoint = changeToHighLowHex(key_settings["other"][1] * 100);
        const pressTrigger = changeToHighLowHex(key_settings["other"][2] * 100);
        const releaseTrigger = changeToHighLowHex(key_settings["other"][3] * 100);
        const bottomProtection = changeToHighLowHex(key_settings["other"][4] * 100);
        let label;
        if (mode === '00') {
          label = (parseHex(triggerPoint) / 100.00).toFixed(2);
        } else {
          label = `${(parseHex(pressTrigger) / 100.00).toFixed(2)}<br/>${(parseHex(releaseTrigger) / 100.00).toFixed(2)}`;
        }
        addToQueue(`19 ${rowHex} ${colHex} ${mode} 00 00 ${triggerPoint} ${pressTrigger} ${releaseTrigger} ${bottomProtection} ${switchType} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
        updateKeycap(rowHex, colHex, {
          ...data.keycaps["performance"][`${rowHex}-${colHex}`],
          label,
          rapidTrigger: {
            ...data.keycaps["performance"][`${rowHex}-${colHex}`]?.rapidTrigger,
            mode: mode,
            triggerPoint: parseHex(triggerPoint) / 100.00,
            pressTravel: parseHex(pressTrigger) / 100.00,
            releaseStroke: parseHex(releaseTrigger) / 100.00,
            bottomProtectionTravel: parseHex(bottomProtection) / 100.00
          }
        }, "performance");
      }
    }
    startProgressTracking(20);
    addToQueue(`41 00`);
  };

  const startProgressTracking = (totalItems) => {
    setFullScreenLoading(true);
    setFullScreenLoadingText([
      t('performance.loading_text_1'),
      t('performance.loading_text_2')
    ][Math.floor(Math.random() * 2)]);
    setFullScreenPercent(0);

    const totalTimeNeeded = totalItems * 120; // 100ms per item
    const startTime = Date.now();

    const interval = setInterval(() => {
      const elapsedTime = Date.now() - startTime;
      const progress = Math.min((elapsedTime / totalTimeNeeded) * 100, 100);

      setFullScreenPercent(progress);

      if (progress >= 100) {
        clearInterval(interval);
        messageApi.success(t('career_preset.apply_settings_success'));
        setFullScreenLoading(false);
        setFullScreenPercent(0);
      }
    }, 50);
  };

  const handleCardClick = (index) => {
    setSelectedCard(index);
  };

  const handleApplySettings = (preset) => {
    if (preset.settings) {
      updateKeyPerformance(preset.settings);
    } else {
      messageApi.error(t('Format error'));
    }
  };

  // 合并内置预设和外部预设，当外部预设为空时不合并
  const allPresets = externalPresets.length > 0 ? [...defaultPresets, ...externalPresets] : defaultPresets;

  return (
    <div className="career-preset-container">
      {contextHolder}
      <div style={{display: "flex", gap: 20, flexWrap: "wrap", marginLeft: "10em", overflow: "auto", overscrollBehavior: "contain"}}>
        {allPresets.map((preset, index) => (
          <div
            key={index}
            style={{ position: 'relative' }}
            onMouseEnter={() => setHoveredCard(index)}
            onMouseLeave={() => setHoveredCard(null)}
          >
            <Card
              style={{
                width: 300,
                marginBottom: 20,
                transition: 'all 0.3s ease-in-out',
                cursor: 'pointer'
              }}
              hoverable
              onClick={() => handleCardClick(index)}
              cover={
                <img
                  alt={preset.title}
                  src={preset.img}
                />
              }
            >
              {preset.desc && (
                <Meta
                  title={preset.title}
                  description={preset.desc}
                />
              )}
              {i18n.language === "zh-CN" && !preset.title && (
                <Meta
                  title={preset.title_zh}
                  description={preset.desc_zh}
                />
              )}
              {i18n.language === "en-US" && !preset.title && (
                <Meta
                  title={preset.title_en}
                  description={preset.desc_en}
                />
              )}
              {i18n.language === "zh-TW" && !preset.title && (
                <Meta
                  title={preset.title_tw}
                  description={preset.desc_tw}
                />
              )}
              {i18n.language === "ja-JP" && !preset.title && (
                <Meta
                  title={preset.title_jp}
                  description={preset.desc_jp}
                />
              )}
              {i18n.language === "ko-KR" && !preset.title && (
                <Meta
                  title={preset.title_ko}
                  description={preset.desc_ko}
                />
              )}
              {i18n.language === "pt-PT" && !preset.title && (
                <Meta
                  title={preset.title_pt}
                  description={preset.desc_pt}
                />
              )}
              <div style={{
                position: 'absolute',
                bottom: '1rem',
                right: '1rem',
                zIndex: 1,
                opacity: hoveredCard === index ? 1 : 0,
                transform: `translateY(${hoveredCard === index ? '0' : '20px'})`,
                transition: 'all 0.3s ease-in-out'
              }}>
                <Button
                  type="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleApplySettings(preset);
                  }}
                >
                  {t('career_preset.apply_settings')}
                </Button>
              </div>
            </Card>
          </div>
        ))}
      </div>
    </div>
  )
}

export default CareerPreset;