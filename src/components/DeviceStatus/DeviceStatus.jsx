import { useEffect } from 'react';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import mqtt from 'mqtt';

const DeviceStatus = () => {
  const { deviceProductId } = useHandleDevice();

  useEffect(() => {
    if (!deviceProductId) return;

    let client = null;
    try {
      // 1. 唯一设备标识（账号 + 设备）
      const deviceId = deviceProductId;
      // 2. 会话ID，每次启动自动生成（也可以用 UUID）
      const sessionId = Date.now().toString();
      // 3. 最终 MQTT clientId
      // const clientId = `${deviceId}_${sessionId}`;

      // console.log("Connecting with clientId:", clientId);

      client = mqtt.connect("wss://key-check-server.iqunix.com/mqtt", {
        clientId: deviceId,
        reconnectPeriod: 1000,
      });

      // 成功连接后订阅踢出指令
      client.on("connect", () => {
        try {
          console.log("Connected to server");
          client.subscribe(`kickout/${deviceId}`, (err) => {
            if (err) {
              console.error("订阅失败:", err);
            }
          });
        } catch (e) {
          console.error("连接事件处理异常:", e);
        }
      });

      // 接收消息
      client.on("message", (topic, message) => {
        try {
          if (topic === `kickout/${deviceId}`) {
            const kickedSessionId = message.toString();
            if (kickedSessionId === sessionId) {
              // alert("你已在其他设备登录，本设备被踢下线");
              client.end(true); // 关闭并阻止重连
              // window.location.href = "about:blank";
            } else {
              console.log("收到踢人消息，但不是我:", kickedSessionId);
            }
          }
        } catch (e) {
          console.error("消息处理异常:", e);
        }
      });

      client.on("error", (err) => {
        // 捕获连接错误，防止报错
        console.error("MQTT 连接错误:", err);
      });

    } catch (e) {
      // 捕获初始化异常，防止报错
      console.error("MQTT 初始化异常:", e);
      if (client && typeof client.end === 'function') {
        try {
          client.end(true);
        } catch (endErr) {
          // 忽略关闭异常
        }
      }
    }

    // 清理函数，断开连接
    return () => {
      if (client && typeof client.end === 'function') {
        try {
          client.end(true);
        } catch (e) {
          // 忽略关闭异常
        }
      }
    };
  }, [deviceProductId]);

  return <div>

  </div>;
};

export default DeviceStatus;