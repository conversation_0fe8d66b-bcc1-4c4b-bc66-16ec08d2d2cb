import React, { useState, useRef, useEffect } from "react";
import { Form, Input, Button, Upload, message, FloatButton, Modal, Image } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import feedbackIcon from '../../assets/feedback.svg';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { useTranslation } from 'react-i18next';

const { TextArea } = Input;

export default function Feedback() {
  const [visible, setVisible] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [fileList, setFileList] = useState([]);
  const [submitting, setSubmitting] = useState(false);
  const formRef = useRef(null);
  const uploadRef = useRef(null);
  const { deviceProductId, firmwareVersion, sn } = useHandleDevice();
  const { t } = useTranslation();

  const openForm = () => setVisible(true);
  // 关闭弹窗
  const closeForm = () => setVisible(false);

  // 粘贴截图上传，限制最多3张
  useEffect(() => {
    const handlePaste = (e) => {
      if (!visible) return; // 仅在表单打开时处理
      const items = e.clipboardData?.items;
      if (!items) return;

      // 计算还能上传几张
      let remain = 3 - fileList.length;
      if (remain <= 0) {
        message.warning(t('feedback.upload_limit_warning'));
        return;
      }

      let added = 0;
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.indexOf("image") !== -1 && added < remain) {
          const file = item.getAsFile();
          if (file) {
            uploadFile(file);
            added++;
          }
        }
      }
      if (added === 0 && remain <= 0) {
        message.warning(t('feedback.upload_limit_warning'));
      }
    };

    document.addEventListener("paste", handlePaste);
    return () => document.removeEventListener("paste", handlePaste);
  }, [visible, fileList.length]);

  const uploadFile = (file) => {
    if (fileList.length >= 3) {
      message.warning(t('feedback.upload_limit_warning'));
      return;
    }
    const formData = new FormData();
    formData.append("image", file);

    fetch(`${import.meta.env.VITE_API_URL}/api/uploads/image`, { method: "POST", body: formData })
      .then((res) => res.json())
      .then((data) => {
        if (data.success) {
          message.success(t('feedback.upload_success'));
          setFileList((prev) => [
            ...prev,
            { uid: Date.now().toString(), name: file.name, status: "done", url: data.data.url },
          ]);
        } else {
          message.error(t('feedback.upload_failed'));
        }
      })
      .catch(() => message.error(t('feedback.upload_error')));
  };

  // 辅助函数：将文件转为base64
  function getBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  }

  const handlePreview = async file => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewOpen(true);
  };

  // 处理点击上传
  const handleUploadChange = ({ file, fileList: newFileList }) => {
    // 只处理新添加的文件
    if (file.status === 'removed') return;
    // 兼容找不到 originFileObj 的情况
    const uploadTarget = file.originFileObj || file;
    if (uploadTarget) {
      uploadFile(uploadTarget);
    }
  };

  const handleSubmit = async (values) => {
    if (submitting) return;
    setSubmitting(true);
    const payload = {
      ...values,
      images: fileList.map((f) => f.url),
      pid: deviceProductId || undefined,
      sn_code: sn || undefined,
      version: firmwareVersion || undefined,
    };
    try {
      // 提交到后台
      const res = await fetch(`${import.meta.env.VITE_API_URL}/api/feedbacks`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(payload)
      });
      const data = await res.json();
      if (data.status === 201) {
        message.success(t('feedback.submit_success'));
        // 只有提交时才清空内容
        setVisible(false);
        setFileList([]);
        // 清空表单内容
        if (formRef.current) {
          formRef.current.resetFields();
        }
      } else {
        message.error(data.message || t('feedback.submit_failed'));
      }
    } catch (err) {
      message.error(t('feedback.submit_error'));
    } finally {
      setSubmitting(false);
    }
  };

  // 上传按钮
  const uploadButton = (
    <div>
      <PlusOutlined />
    </div>
  );

  return (
    <>
      {/* 左侧导航栏按钮替代悬浮按钮 */}
      <div style={{position: 'fixed', bottom: '7.5em', marginLeft: '1em', zIndex: 1000}}>
        <Button
          size="large"
          type="primary"
          style={{
            backgroundColor: '#151619',
            borderColor: 'transparent',
            color: '#7A7B7C',
            width: '240px',
            fontSize: '14px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          onClick={openForm}
        >
          {t('feedback.title')}
        </Button>
      </div>

      {/* antd Modal 弹窗 */}
      <Modal
        open={visible}
        title={t('feedback.title')}
        onCancel={closeForm}
        footer={null}
        width={500}
        centered
        destroyOnClose
        maskClosable={false}
        styles={{
          body: {
            maxHeight: '80vh',
            overflowY: 'auto',
            minHeight: '520px',
            paddingTop: '24px'
          }
        }}
      >
        <Form layout="vertical" onFinish={handleSubmit} ref={formRef}>
          <Form.Item
            label={t('feedback.contact_label')}
            name="contact"
            rules={[{ required: false, message: t('feedback.contact_required') }]}
          >
            <Input placeholder={t('feedback.contact_placeholder')}  />
          </Form.Item>

          <Form.Item
            label={t('feedback.description_label')}
            name="description"
            rules={[{ required: true, message: t('feedback.description_required') }]}
          >
            <TextArea
              placeholder={t('feedback.description_placeholder')}
              rows={6}
              maxLength={500}
              showCount={true}
            />
          </Form.Item>

          <div style={{ height: '120px' }}>
            <Upload
              ref={uploadRef}
              fileList={fileList}
              listType="picture-card"
              showUploadList={{
                showPreviewIcon: true,
                showRemoveIcon: true,
              }}
              beforeUpload={(file, fileListNew) => {
                if (fileList.length >= 3) {
                  message.warning(t('feedback.upload_limit_warning'));
                  return Upload.LIST_IGNORE;
                }
                return false;
              }}
              onRemove={(file) => setFileList(fileList.filter((f) => f.uid !== file.uid))}
              onPreview={handlePreview}
              onChange={handleUploadChange}
              accept="image/*"
            >
              {fileList.length < 3 && uploadButton}
            </Upload>
          </div>

          {previewImage && (
            <Image
              wrapperStyle={{ display: 'none' }}
              preview={{
                visible: previewOpen,
                onVisibleChange: visible => setPreviewOpen(visible),
                afterOpenChange: visible => !visible && setPreviewImage(''),
              }}
              src={previewImage}
            />
          )}
          <Form.Item>
            <Button type="primary" htmlType="submit" block loading={submitting} disabled={submitting}>
              {t('feedback.submit_button')}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
