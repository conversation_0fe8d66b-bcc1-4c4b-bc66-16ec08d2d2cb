.gamepad-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 20px;
}

.gamepad-content {
  width: 100%;
  max-width: 1120px;
  margin-top: 20px;
}

.connection-status {
  text-align: center;
  margin-bottom: 16px;
}

.joysticks-section {
  margin-bottom: 32px;
}

.joysticks-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 40px;
  margin-top: 20px;
}

.joystick-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #1C1D22;
  border-radius: 12px;
  border: 1px solid #2A2B30;
}

.joystick-title {
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
  margin-bottom: 16px;
}

.joystick-svg {
  background: #161720;
  border-radius: 50%;
  border: 2px solid #3A3B40;
}

.joystick-indicator {
  transition: all 0.1s ease;
  filter: drop-shadow(0 0 4px #1890ff);
}

.joystick-values {
  margin-top: 12px;
  text-align: center;
  font-family: monospace;
  font-size: 12px;
  color: #B0B0B0;
}

.joystick-values div {
  margin: 2px 0;
}

.triggers-section {
  margin-bottom: 32px;
}

.triggers-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 40px;
  margin-top: 20px;
}

.trigger-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #1C1D22;
  border-radius: 12px;
  border: 1px solid #2A2B30;
  min-width: 200px;
}

.trigger-title {
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
  margin-bottom: 16px;
}

.trigger-progress {
  width: 100%;
  margin-bottom: 12px;
}

.trigger-value {
  font-family: monospace;
  font-size: 14px;
  color: #B0B0B0;
  text-align: center;
}

/* 按钮测试区域 */
.buttons-section {
  margin-bottom: 32px;
}

.buttons-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
  margin-top: 20px;
}

.button-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: #1C1D22;
  border-radius: 8px;
  border: 1px solid #2A2B30;
  transition: all 0.2s ease;
}

.button-item.pressed {
  background: #1890ff;
  border-color: #1890ff;
  transform: scale(0.95);
}

.button-label {
  font-size: 12px;
  color: #B0B0B0;
  margin-bottom: 4px;
}

.button-item.pressed .button-label {
  color: #FFFFFF;
}

.button-indicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3A3B40;
  transition: all 0.2s ease;
}

.button-item.pressed .button-indicator {
  background: #FFFFFF;
}

/* 测试状态样式 */
.testing-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #FF4D4F;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  animation: testing-pulse 2s infinite;
  z-index: 1000;
}

@keyframes testing-pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .joysticks-container,
  .triggers-container {
    flex-direction: column;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .gamepad-container {
    padding: 10px;
  }
  
  .joystick-container,
  .trigger-container {
    padding: 15px;
  }
  
  .joystick-svg {
    width: 100px;
    height: 100px;
  }
  
  .trigger-container {
    min-width: 150px;
  }
}

/* 自定义进度条样式 */
.ant-progress-bg {
  transition: all 0.1s ease !important;
}

/* 标题样式 */
.ant-typography h4 {
  color: #FFFFFF !important;
  margin-bottom: 16px !important;
}

/* 分割线样式 */
.ant-divider {
  border-color: #2A2B30 !important;
}

/* 标签样式 */
.ant-tag {
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 16px;
}

/* 无手柄连接时的提示样式 */
.no-gamepad-message {
  text-align: center;
  color: #888;
  font-size: 1.1em;
  padding: 2em 0;
  background: #1C1D22;
  border-radius: 8px;
  border: 1px dashed #2A2B30;
}

/* 数据显示区域 */
.data-display {
  background: #161720;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
  border: 1px solid #2A2B30;
}

.data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #2A2B30;
}

.data-row:last-child {
  border-bottom: none;
}

.data-label {
  color: #B0B0B0;
  font-size: 14px;
}

.data-value {
  color: #FFFFFF;
  font-family: monospace;
  font-size: 14px;
}

/* 连接状态指示器 */
.connection-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.connection-indicator.connected {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  border: 1px solid rgba(82, 196, 26, 0.3);
}

.connection-indicator.disconnected {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  border: 1px solid rgba(255, 77, 79, 0.3);
}

.connection-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: connection-pulse 2s infinite;
}

@keyframes connection-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
