import React, { useState, useEffect, useRef } from 'react';
import { Button, Card, message, Typography, Space, Tag, Divider, Progress } from 'antd';
import { PlayCircleOutlined, StopOutlined, ReloadOutlined } from '@ant-design/icons';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { useTranslation } from 'react-i18next';
import './Gamepad.css';

const { Title, Text } = Typography;

const Gamepad = () => {
  const { t } = useTranslation();
  const { addToQueue, dataQueue } = useHandleDevice();
  const [isConnected, setIsConnected] = useState(false);
  const [gamepadData, setGamepadData] = useState({
    leftStick: { x: 0, y: 0 },
    rightStick: { x: 0, y: 0 },
    leftTrigger: 0,
    rightTrigger: 0,
    buttons: {}
  });
  const [isTesting, setIsTesting] = useState(false);
  const animationFrameRef = useRef();
  const [messageApi, contextHolder] = message.useMessage();

  // 检测手柄连接
  useEffect(() => {
    const checkGamepadConnection = () => {
      const gamepads = navigator.getGamepads();
      const connectedGamepad = Array.from(gamepads).find(gamepad => gamepad !== null);
      setIsConnected(!!connectedGamepad);
    };

    // 初始检测
    checkGamepadConnection();

    // 监听手柄连接事件
    const handleGamepadConnected = (e) => {
      console.log('Gamepad connected:', e.gamepad);
      setIsConnected(true);
      messageApi.success(`${t('gamepad.connected')}: ${e.gamepad.id}`);
    };

    const handleGamepadDisconnected = (e) => {
      console.log('Gamepad disconnected:', e.gamepad);
      setIsConnected(false);
      setIsTesting(false); // 断开连接时停止测试
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      messageApi.warning(t('gamepad.disconnected'));
    };

    window.addEventListener('gamepadconnected', handleGamepadConnected);
    window.addEventListener('gamepaddisconnected', handleGamepadDisconnected);

    return () => {
      window.removeEventListener('gamepadconnected', handleGamepadConnected);
      window.removeEventListener('gamepaddisconnected', handleGamepadDisconnected);
    };
  }, [messageApi, t]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  // 读取手柄数据
  const readGamepadData = () => {
    const gamepads = navigator.getGamepads();
    const gamepad = Array.from(gamepads).find(gp => gp !== null);

    if (gamepad) {
      const newData = {
        leftStick: {
          x: gamepad.axes[0] || 0,
          y: gamepad.axes[1] || 0
        },
        rightStick: {
          x: gamepad.axes[2] || 0,
          y: gamepad.axes[3] || 0
        },
        leftTrigger: gamepad.buttons[6]?.value || 0,
        rightTrigger: gamepad.buttons[7]?.value || 0,
        buttons: gamepad.buttons.reduce((acc, button, index) => {
          acc[index] = button.pressed;
          return acc;
        }, {})
      };

      setGamepadData(newData);
    }

    if (isTesting) {
      animationFrameRef.current = requestAnimationFrame(readGamepadData);
    }
  };

  // 开始测试
  const startTesting = () => {
    if (!isConnected) {
      messageApi.warning(t('gamepad.no_gamepad_connected'));
      return;
    }

    setIsTesting(true);
    messageApi.info(t('gamepad.testing_started'));
    readGamepadData();
  };

  // 停止测试
  const stopTesting = () => {
    setIsTesting(false);
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    messageApi.info(t('gamepad.testing_stopped'));
  };

  // 重置数据
  const resetData = () => {
    setGamepadData({
      leftStick: { x: 0, y: 0 },
      rightStick: { x: 0, y: 0 },
      leftTrigger: 0,
      rightTrigger: 0,
      buttons: {}
    });
  };

  // 渲染摇杆
  const renderJoystick = (stickData, title) => {
    const centerX = 60; // 圆心X坐标
    const centerY = 60; // 圆心Y坐标
    const radius = 50; // 圆的半径
    const indicatorRadius = 8; // 指示点半径

    // 计算指示点位置
    const indicatorX = centerX + (stickData.x * radius * 0.8);
    const indicatorY = centerY + (stickData.y * radius * 0.8);

    return (
      <div className="joystick-container">
        <div className="joystick-title">{title}</div>
        <svg width="120" height="120" className="joystick-svg">
          {/* 外圆 */}
          <circle
            cx={centerX}
            cy={centerY}
            r={radius}
            fill="none"
            stroke="#3A3B40"
            strokeWidth="2"
          />
          {/* 内圆 */}
          <circle
            cx={centerX}
            cy={centerY}
            r={radius * 0.3}
            fill="none"
            stroke="#5A5B60"
            strokeWidth="1"
          />
          {/* 十字线 */}
          <line
            x1={centerX - radius}
            y1={centerY}
            x2={centerX + radius}
            y2={centerY}
            stroke="#5A5B60"
            strokeWidth="1"
          />
          <line
            x1={centerX}
            y1={centerY - radius}
            x2={centerX}
            y2={centerY + radius}
            stroke="#5A5B60"
            strokeWidth="1"
          />
          {/* 指示点 */}
          <circle
            cx={indicatorX}
            cy={indicatorY}
            r={indicatorRadius}
            fill="#1890ff"
            className="joystick-indicator"
          />
        </svg>
        <div className="joystick-values">
          <div>X: {stickData.x.toFixed(3)}</div>
          <div>Y: {stickData.y.toFixed(3)}</div>
        </div>
      </div>
    );
  };

  // 渲染扳机键
  const renderTrigger = (value, title) => {
    return (
      <div className="trigger-container">
        <div className="trigger-title">{title}</div>
        <div className="trigger-progress">
          <Progress
            percent={Math.round(value * 100)}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            showInfo={true}
            format={(percent) => `${percent}%`}
          />
        </div>
        <div className="trigger-value">
          {value.toFixed(3)}
        </div>
      </div>
    );
  };

  return (
    <div className="gamepad-container">
      {contextHolder}
      {isTesting && (
        <div className="testing-indicator">
          {t('gamepad.testing_in_progress')}
        </div>
      )}
      <div className="gamepad-content">
        <div className="d-flex justify-content-between" style={{marginTop: '-0.8em'}}>
          <div className="d-flex align-items-center">
            <div style={{height: '1.5em', width: '0.2em', borderRadius: '0.25em', background: 'var(--bs-primary)', marginRight: '0.5em'}}></div>
            <div style={{fontSize: '1.2em', fontWeight: 'bold'}}>{t('gamepad.title')}</div>
          </div>
          <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
            <Button
              key="reset"
              icon={<ReloadOutlined />}
              onClick={resetData}
              disabled={isTesting}
            >
              {t('gamepad.reset')}
            </Button>
            <Button
              key="test"
              color={isTesting ? "danger" : "primary"}
              icon={isTesting ? <StopOutlined /> : <PlayCircleOutlined />}
              variant="solid"
              onClick={() => {
                if (isTesting) {
                  stopTesting();
                } else {
                  startTesting();
                }
              }}
            >
              {isTesting ? t('gamepad.stop_testing') : t('gamepad.start_testing')}
            </Button>
          </div>
        </div>

        <div
          style={{
            width: '752px',
            overflow: 'auto',
            borderRadius: '1em',
            padding: '2em',
            alignContent: 'flex-start',
            marginTop: '0.7em'
          }}
          className='custom-card-container'
        >
          {/* 连接状态 */}
          <div className="connection-status">
            <Tag color={isConnected ? 'green' : 'red'}>
              {isConnected ? t('gamepad.connected') : t('gamepad.disconnected')}
            </Tag>
          </div>

          <Divider />

          {/* 摇杆测试区域 */}
          <div className="joysticks-section">
            <Title level={4}>{t('gamepad.joysticks_test')}</Title>
            <div className="joysticks-container">
              {renderJoystick(gamepadData.leftStick, t('gamepad.left_stick'))}
              {renderJoystick(gamepadData.rightStick, t('gamepad.right_stick'))}
            </div>
          </div>

          <Divider />

          {/* 扳机键测试区域 */}
          <div className="triggers-section">
            <Title level={4}>{t('gamepad.triggers_test')}</Title>
            <div className="triggers-container">
              {renderTrigger(gamepadData.leftTrigger, t('gamepad.left_trigger'))}
              {renderTrigger(gamepadData.rightTrigger, t('gamepad.right_trigger'))}
            </div>
          </div>

          <Divider />

          {/* 按钮测试区域 */}
          <div className="buttons-section">
            <Title level={4}>{t('gamepad.buttons_test')}</Title>
            <div className="buttons-container">
              {Array.from({ length: 16 }, (_, index) => (
                <div
                  key={index}
                  className={`button-item ${gamepadData.buttons[index] ? 'pressed' : ''}`}
                >
                  <div className="button-label">
                    {t('gamepad.button')} {index}
                  </div>
                  <div className="button-indicator"></div>
                </div>
              ))}
            </div>
          </div>

          {!isConnected && (
            <div style={{ textAlign: 'center', color: '#888', fontSize: '1.1em', padding: '2em 0' }}>
              {t('gamepad.no_gamepad_message')}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Gamepad;
