<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gamepad API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #161720;
            color: white;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected {
            background: #52c41a;
        }
        .disconnected {
            background: #ff4d4f;
        }
        .joystick {
            display: inline-block;
            margin: 20px;
            text-align: center;
        }
        .joystick svg {
            background: #1C1D22;
            border-radius: 50%;
            border: 2px solid #3A3B40;
        }
        .trigger {
            display: inline-block;
            margin: 20px;
            text-align: center;
            width: 200px;
        }
        .trigger-bar {
            width: 100%;
            height: 20px;
            background: #3A3B40;
            border-radius: 10px;
            overflow: hidden;
        }
        .trigger-fill {
            height: 100%;
            background: linear-gradient(90deg, #108ee9, #87d068);
            transition: width 0.1s ease;
        }
        .buttons {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 10px;
            margin: 20px 0;
        }
        .button {
            padding: 10px;
            background: #1C1D22;
            border: 1px solid #2A2B30;
            border-radius: 5px;
            text-align: center;
            transition: all 0.2s ease;
        }
        .button.pressed {
            background: #1890ff;
            transform: scale(0.95);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gamepad API Test</h1>
        <div id="status" class="status disconnected">No gamepad connected</div>
        
        <h2>Joysticks</h2>
        <div class="joystick">
            <h3>Left Stick</h3>
            <svg width="120" height="120" id="leftStick">
                <circle cx="60" cy="60" r="50" fill="none" stroke="#3A3B40" stroke-width="2"/>
                <circle cx="60" cy="60" r="15" fill="none" stroke="#5A5B60" stroke-width="1"/>
                <line x1="10" y1="60" x2="110" y2="60" stroke="#5A5B60" stroke-width="1"/>
                <line x1="60" y1="10" x2="60" y2="110" stroke="#5A5B60" stroke-width="1"/>
                <circle id="leftIndicator" cx="60" cy="60" r="8" fill="#1890ff"/>
            </svg>
            <div id="leftValues">X: 0.000, Y: 0.000</div>
        </div>
        
        <div class="joystick">
            <h3>Right Stick</h3>
            <svg width="120" height="120" id="rightStick">
                <circle cx="60" cy="60" r="50" fill="none" stroke="#3A3B40" stroke-width="2"/>
                <circle cx="60" cy="60" r="15" fill="none" stroke="#5A5B60" stroke-width="1"/>
                <line x1="10" y1="60" x2="110" y2="60" stroke="#5A5B60" stroke-width="1"/>
                <line x1="60" y1="10" x2="60" y2="110" stroke="#5A5B60" stroke-width="1"/>
                <circle id="rightIndicator" cx="60" cy="60" r="8" fill="#1890ff"/>
            </svg>
            <div id="rightValues">X: 0.000, Y: 0.000</div>
        </div>
        
        <h2>Triggers</h2>
        <div class="trigger">
            <h3>Left Trigger</h3>
            <div class="trigger-bar">
                <div id="leftTriggerFill" class="trigger-fill" style="width: 0%"></div>
            </div>
            <div id="leftTriggerValue">0.000</div>
        </div>
        
        <div class="trigger">
            <h3>Right Trigger</h3>
            <div class="trigger-bar">
                <div id="rightTriggerFill" class="trigger-fill" style="width: 0%"></div>
            </div>
            <div id="rightTriggerValue">0.000</div>
        </div>
        
        <h2>Buttons</h2>
        <div class="buttons" id="buttons">
            <!-- Buttons will be generated by JavaScript -->
        </div>
    </div>

    <script>
        let gamepadConnected = false;
        let animationId;

        // Initialize buttons
        function initButtons() {
            const buttonsContainer = document.getElementById('buttons');
            for (let i = 0; i < 16; i++) {
                const button = document.createElement('div');
                button.className = 'button';
                button.id = `button${i}`;
                button.textContent = `Button ${i}`;
                buttonsContainer.appendChild(button);
            }
        }

        // Update gamepad data
        function updateGamepad() {
            const gamepads = navigator.getGamepads();
            const gamepad = Array.from(gamepads).find(gp => gp !== null);
            
            if (gamepad) {
                // Update joysticks
                updateJoystick('left', gamepad.axes[0] || 0, gamepad.axes[1] || 0);
                updateJoystick('right', gamepad.axes[2] || 0, gamepad.axes[3] || 0);
                
                // Update triggers
                updateTrigger('left', gamepad.buttons[6]?.value || 0);
                updateTrigger('right', gamepad.buttons[7]?.value || 0);
                
                // Update buttons
                gamepad.buttons.forEach((button, index) => {
                    updateButton(index, button.pressed);
                });
            }
            
            if (gamepadConnected) {
                animationId = requestAnimationFrame(updateGamepad);
            }
        }

        function updateJoystick(side, x, y) {
            const indicator = document.getElementById(`${side}Indicator`);
            const values = document.getElementById(`${side}Values`);
            
            const centerX = 60;
            const centerY = 60;
            const radius = 40;
            
            const indicatorX = centerX + (x * radius);
            const indicatorY = centerY + (y * radius);
            
            indicator.setAttribute('cx', indicatorX);
            indicator.setAttribute('cy', indicatorY);
            
            values.textContent = `X: ${x.toFixed(3)}, Y: ${y.toFixed(3)}`;
        }

        function updateTrigger(side, value) {
            const fill = document.getElementById(`${side}TriggerFill`);
            const valueDisplay = document.getElementById(`${side}TriggerValue`);
            
            fill.style.width = `${value * 100}%`;
            valueDisplay.textContent = value.toFixed(3);
        }

        function updateButton(index, pressed) {
            const button = document.getElementById(`button${index}`);
            if (button) {
                if (pressed) {
                    button.classList.add('pressed');
                } else {
                    button.classList.remove('pressed');
                }
            }
        }

        // Event listeners
        window.addEventListener('gamepadconnected', (e) => {
            console.log('Gamepad connected:', e.gamepad);
            gamepadConnected = true;
            document.getElementById('status').textContent = `Gamepad connected: ${e.gamepad.id}`;
            document.getElementById('status').className = 'status connected';
            updateGamepad();
        });

        window.addEventListener('gamepaddisconnected', (e) => {
            console.log('Gamepad disconnected:', e.gamepad);
            gamepadConnected = false;
            document.getElementById('status').textContent = 'No gamepad connected';
            document.getElementById('status').className = 'status disconnected';
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        });

        // Initialize
        initButtons();
        
        // Check for already connected gamepads
        const gamepads = navigator.getGamepads();
        const connectedGamepad = Array.from(gamepads).find(gp => gp !== null);
        if (connectedGamepad) {
            gamepadConnected = true;
            document.getElementById('status').textContent = `Gamepad connected: ${connectedGamepad.id}`;
            document.getElementById('status').className = 'status connected';
            updateGamepad();
        }
    </script>
</body>
</html>
