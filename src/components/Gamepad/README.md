# Gamepad 功能

## 概述

Gamepad 功能模块为 IQUNIX 磁轴驱动提供了完整的手柄测试功能，包括摇杆测试、扳机键测试和按钮测试。该功能参考了宏录制的样式设计，提供了一致的用户体验。

## 功能特性

### 1. 摇杆测试
- **左摇杆和右摇杆**：实时显示摇杆的 X、Y 轴位置
- **圆形可视化**：使用圆形图表和指示点显示摇杆位置
- **数值显示**：精确显示摇杆的坐标值（-1.000 到 1.000）

### 2. 扳机键测试
- **左扳机和右扳机**：实时显示扳机键的按压程度
- **进度条显示**：使用渐变色进度条显示扳机值
- **百分比和数值**：同时显示百分比和精确数值

### 3. 按钮测试
- **16个按钮**：支持标准手柄的所有按钮测试
- **实时反馈**：按钮按下时立即显示视觉反馈
- **状态指示**：清晰的按下/释放状态显示

### 4. 连接状态
- **自动检测**：自动检测手柄连接和断开
- **状态提示**：实时显示连接状态
- **消息通知**：连接/断开时显示通知消息

## 技术实现

### 核心技术
- **Gamepad API**：使用浏览器原生 Gamepad API
- **React Hooks**：使用 useState、useEffect、useRef 管理状态
- **requestAnimationFrame**：高性能的实时数据更新
- **SVG 绘图**：使用 SVG 绘制摇杆可视化

### 组件结构
```
src/components/Gamepad/
├── Gamepad.jsx          # 主组件
├── Gamepad.css          # 样式文件
├── GamepadTest.html     # 独立测试页面
└── README.md           # 说明文档
```

### 数据结构
```javascript
gamepadData = {
  leftStick: { x: 0, y: 0 },      // 左摇杆
  rightStick: { x: 0, y: 0 },     // 右摇杆
  leftTrigger: 0,                 // 左扳机 (0-1)
  rightTrigger: 0,                // 右扳机 (0-1)
  buttons: {}                     // 按钮状态对象
}
```

## 样式设计

### 设计原则
- **一致性**：与宏录制功能保持一致的设计风格
- **深色主题**：适配应用的深色主题
- **响应式**：支持不同屏幕尺寸
- **交互反馈**：提供清晰的视觉反馈

### 颜色方案
- **背景色**：#1C1D22（卡片背景）
- **边框色**：#2A2B30（边框）
- **主色调**：#1890ff（指示器、进度条）
- **文字色**：#FFFFFF（标题）、#B0B0B0（数值）

### 动画效果
- **摇杆指示器**：平滑的位置过渡
- **按钮反馈**：按下时的缩放效果
- **进度条**：平滑的宽度变化
- **连接状态**：脉冲动画效果

## 国际化支持

支持以下语言：
- 简体中文 (zh-CN)
- 繁体中文 (zh-TW)
- 英语 (en-US)
- 日语 (ja-JP)
- 韩语 (ko-KR)
- 葡萄牙语 (pt-PT)

### 翻译键
```json
{
  "gamepad": {
    "title": "手柄功能",
    "connected": "已连接",
    "disconnected": "未连接",
    "start_testing": "开始测试",
    "stop_testing": "停止测试",
    "reset": "重置",
    "joysticks_test": "摇杆测试",
    "triggers_test": "扳机键测试",
    "buttons_test": "按钮测试",
    "left_stick": "左摇杆",
    "right_stick": "右摇杆",
    "left_trigger": "左扳机键",
    "right_trigger": "右扳机键",
    "button": "按钮"
  }
}
```

## 使用方法

### 1. 连接手柄
- 将手柄连接到计算机（USB 或蓝牙）
- 确保手柄被系统识别
- 打开驱动程序，点击侧边栏的"手柄功能"

### 2. 开始测试
- 点击"开始测试"按钮
- 移动摇杆、按压扳机键和按钮
- 观察实时反馈

### 3. 停止测试
- 点击"停止测试"按钮停止实时更新
- 点击"重置"按钮清空所有数据

## 浏览器兼容性

### 支持的浏览器
- Chrome 21+
- Firefox 29+
- Safari 10.1+
- Edge 12+

### Gamepad API 支持
- 标准 Gamepad API
- 自动检测手柄连接/断开
- 实时数据读取

## 故障排除

### 常见问题

1. **手柄无法检测**
   - 确保手柄正确连接
   - 检查浏览器是否支持 Gamepad API
   - 尝试在其他应用中测试手柄

2. **数据不更新**
   - 确保已点击"开始测试"
   - 检查浏览器控制台是否有错误
   - 尝试刷新页面

3. **按钮映射不正确**
   - 不同品牌手柄的按钮映射可能不同
   - 这是正常现象，API 会报告实际的按钮索引

### 调试工具
- 使用 `GamepadTest.html` 进行独立测试
- 浏览器开发者工具查看 Gamepad API 状态
- 控制台日志查看连接事件

## 开发说明

### 添加新功能
1. 在 `Gamepad.jsx` 中添加新的渲染函数
2. 在 `Gamepad.css` 中添加对应样式
3. 在国际化文件中添加翻译

### 性能优化
- 使用 `requestAnimationFrame` 确保流畅更新
- 避免不必要的 DOM 操作
- 合理使用 React 的 memo 和 callback

### 测试
- 使用不同品牌的手柄测试兼容性
- 测试连接/断开场景
- 验证所有按钮和轴的响应
