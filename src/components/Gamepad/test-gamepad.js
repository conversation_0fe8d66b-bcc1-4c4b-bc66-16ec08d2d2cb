// Gamepad 功能测试脚本
// 在浏览器控制台中运行此脚本来测试 Gamepad API

console.log('🎮 Gamepad API 测试开始...');

// 检查浏览器支持
if (!navigator.getGamepads) {
  console.error('❌ 浏览器不支持 Gamepad API');
} else {
  console.log('✅ 浏览器支持 Gamepad API');
}

// 检查当前连接的手柄
function checkConnectedGamepads() {
  const gamepads = navigator.getGamepads();
  const connectedGamepads = Array.from(gamepads).filter(gp => gp !== null);
  
  console.log(`🔍 检测到 ${connectedGamepads.length} 个手柄:`);
  connectedGamepads.forEach((gamepad, index) => {
    console.log(`  ${index + 1}. ${gamepad.id}`);
    console.log(`     - 按钮数量: ${gamepad.buttons.length}`);
    console.log(`     - 轴数量: ${gamepad.axes.length}`);
    console.log(`     - 时间戳: ${gamepad.timestamp}`);
  });
  
  return connectedGamepads;
}

// 监听手柄连接事件
window.addEventListener('gamepadconnected', (e) => {
  console.log('🔌 手柄已连接:', e.gamepad.id);
  checkConnectedGamepads();
});

window.addEventListener('gamepaddisconnected', (e) => {
  console.log('🔌 手柄已断开:', e.gamepad.id);
  checkConnectedGamepads();
});

// 实时监控手柄数据
let monitoringInterval = null;

function startMonitoring() {
  if (monitoringInterval) {
    console.log('⚠️ 监控已在运行中');
    return;
  }
  
  console.log('📊 开始监控手柄数据...');
  monitoringInterval = setInterval(() => {
    const gamepads = navigator.getGamepads();
    const gamepad = Array.from(gamepads).find(gp => gp !== null);
    
    if (gamepad) {
      // 检查摇杆
      const leftStick = {
        x: (gamepad.axes[0] || 0).toFixed(3),
        y: (gamepad.axes[1] || 0).toFixed(3)
      };
      const rightStick = {
        x: (gamepad.axes[2] || 0).toFixed(3),
        y: (gamepad.axes[3] || 0).toFixed(3)
      };
      
      // 检查扳机键
      const leftTrigger = (gamepad.buttons[6]?.value || 0).toFixed(3);
      const rightTrigger = (gamepad.buttons[7]?.value || 0).toFixed(3);
      
      // 检查按下的按钮
      const pressedButtons = gamepad.buttons
        .map((button, index) => button.pressed ? index : null)
        .filter(index => index !== null);
      
      // 只在有变化时输出
      if (leftStick.x !== '0.000' || leftStick.y !== '0.000' || 
          rightStick.x !== '0.000' || rightStick.y !== '0.000' ||
          leftTrigger !== '0.000' || rightTrigger !== '0.000' ||
          pressedButtons.length > 0) {
        
        console.clear();
        console.log('🎮 实时手柄数据:');
        console.log(`📍 左摇杆: X=${leftStick.x}, Y=${leftStick.y}`);
        console.log(`📍 右摇杆: X=${rightStick.x}, Y=${rightStick.y}`);
        console.log(`🎯 左扳机: ${leftTrigger}`);
        console.log(`🎯 右扳机: ${rightTrigger}`);
        if (pressedButtons.length > 0) {
          console.log(`🔘 按下的按钮: ${pressedButtons.join(', ')}`);
        }
      }
    }
  }, 100);
}

function stopMonitoring() {
  if (monitoringInterval) {
    clearInterval(monitoringInterval);
    monitoringInterval = null;
    console.log('⏹️ 停止监控手柄数据');
  } else {
    console.log('⚠️ 监控未在运行');
  }
}

// 测试所有按钮
function testAllButtons() {
  console.log('🧪 开始按钮测试 - 请按下每个按钮...');
  const testedButtons = new Set();
  
  const testInterval = setInterval(() => {
    const gamepads = navigator.getGamepads();
    const gamepad = Array.from(gamepads).find(gp => gp !== null);
    
    if (gamepad) {
      gamepad.buttons.forEach((button, index) => {
        if (button.pressed && !testedButtons.has(index)) {
          testedButtons.add(index);
          console.log(`✅ 按钮 ${index} 测试通过`);
        }
      });
      
      if (testedButtons.size >= gamepad.buttons.length) {
        clearInterval(testInterval);
        console.log('🎉 所有按钮测试完成！');
      }
    }
  }, 50);
  
  // 30秒后自动停止测试
  setTimeout(() => {
    clearInterval(testInterval);
    console.log(`⏰ 按钮测试超时，已测试 ${testedButtons.size} 个按钮`);
  }, 30000);
}

// 导出测试函数到全局
window.gamepadTest = {
  check: checkConnectedGamepads,
  startMonitoring,
  stopMonitoring,
  testButtons: testAllButtons
};

// 初始检查
checkConnectedGamepads();

console.log(`
🎮 Gamepad 测试工具已加载！

可用命令:
- gamepadTest.check()          // 检查连接的手柄
- gamepadTest.startMonitoring() // 开始实时监控
- gamepadTest.stopMonitoring()  // 停止监控
- gamepadTest.testButtons()     // 测试所有按钮

提示: 连接手柄后会自动检测，移动摇杆或按按钮来测试功能
`);

// 如果已有手柄连接，提示用户
const initialGamepads = checkConnectedGamepads();
if (initialGamepads.length > 0) {
  console.log('💡 检测到手柄已连接，可以开始测试了！');
  console.log('💡 运行 gamepadTest.startMonitoring() 开始实时监控');
}
