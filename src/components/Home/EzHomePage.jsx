import { WaveGradient } from "wave-gradient";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';

const EzHomePage = ({ showHome, setShowHome, setShowEnterDriver, showEnterDriver }) => {
  const { t } = useTranslation();
  const { handleOpenDevice } = useHandleDevice();

  useEffect(() => {
    const canvasElement = document.querySelector("canvas");

    try {
      // Throws if it can't get a WebGL 2.0 context. For example, if the
      // browser does not support it.
      const gradient = new WaveGradient(canvasElement, {
        colors: ["#101e48", "#101e48", "#155db1", "#155db1"],
        fps: 30,
        seed: 0.5,
        speed: 4.25,
      });
    } catch (e) {
      console.error(e);
    }
  }, []);

  return (
    <div>
      <div style={{
        width: '100%',
        height: 'calc(100vh - 73px)',
        position: 'relative',
        overflow: 'hidden',
        zIndex: 1
      }}>
        <canvas
          id="wave-gradient"
          style={{
            width: '100%',
            height: 'calc(100vh - 73px)',
            display: 'block',
            filter: 'blur(16px)',
            WebkitFilter: 'blur(16px)',
          }}
        ></canvas>
        <img
          src="/home/<USER>"
          alt="ez63_home"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: 'calc(100vh - 73px)',
            objectFit: 'cover',
            pointerEvents: 'none',
            zIndex: 1,
          }}
        />
      </div>
      <div style={{
        position: 'absolute',
        left: '50%',
        transform: 'translateX(-50%)',
        width: '1000px',
        bottom: '0px',
        objectFit: 'cover',
        zIndex: 12,
      }}>
        <div style={{ transition: 'all 0.3s ease-in', height: 'calc(100vh - 73px)', width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <div style={{ fontSize: '100px', fontWeight: 'bold', color: '#ffffff', marginTop: '102px', fontStyle: 'italic', width: '100vw', textAlign: 'center' }}>{t('play_your_way_with_ez')}</div>
          <div style={{ fontSize: '1.75em', color: '#ffffff', marginTop: '1.8em', width: '800px', textAlign: 'center', lineHeight: '1.5em' }}>{t('effortlessly_conlgure_your_ez')}<br/>{t('effortlessly_conlgure_your_ez2')}</div>
          <div className="home-get-start" onClick={() => {
            if (showEnterDriver) {
              setShowHome(false);
            } else {
              handleOpenDevice();
            }
          }}>{showEnterDriver ? t('enter_driver') : t('get_start')}</div>
        </div>
      </div>
    </div>
  )
}

export default EzHomePage;