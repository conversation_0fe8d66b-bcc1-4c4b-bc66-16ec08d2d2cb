
import { useTranslation } from 'react-i18next';
import React, {useState, useEffect, useContext} from 'react';
import { KeyboardContext } from '../Keyboard/KeyboardContext.jsx';
import { But<PERSON>, Alert, ConfigProvider } from 'antd';
import { InfoCircleFilled } from '@ant-design/icons';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import GetTopBottomVoltage from '../KeyTest/GetTopBottomVoltage'
import '../Keyboard/Keyboard.css'
import { HandleLayout, getLayoutJsonByPid } from '../Keyboard/HandleLayout';

function KeyboardTest({itemPage='', tmpMeedAdjustKeys=[]}) {
  const { t } = useTranslation();
  const { dataQueue, addToQueue, deviceProductId, adjustTimes, setAdjustStatus } = useHandleDevice();
  const [buttonDisable, setButtonDisable] = useState(false)
  const { data } = useContext(KeyboardContext);

  const [pressStatus, setPressStatus] = useState('none')

  const handleStartButton = ()=>{
    setButtonDisable(true)
    setPressStatus('start')
    addToQueue('4F')
    setAdjustStatus('start')
  }

  const handleSaveButton = ()=>{
    setButtonDisable(false)
    setAdjustStatus('over')
    setPressStatus('over');
    GetTopBottomVoltage(dataQueue);
    addToQueue('51')
  }

  useEffect(() => {
    if (buttonDisable) {
      let currentTotalKeys = 0
      const totalKeys = getLayoutJsonByPid(deviceProductId).reduce((sum, row) => sum + row.keys.length, 0);
      if (adjustTimes === 2) {
        // 检查有多少个按键达到了目标次数
        Object.keys(data.keycaps["00"]).forEach(key => {
          const count = data.keycaps["00"][key].alreadyAdjustTimes || 0;
          if (count >= adjustTimes) {
            currentTotalKeys += 1;
          }
        });
        if (currentTotalKeys >= totalKeys) {
          setPressStatus('pressover');
        }
      } else if (adjustTimes === 5) {
        // 检查有多少个按键达到了目标次数
        tmpMeedAdjustKeys.forEach(key => {
          const count = data.keycaps["00"][key].alreadyAdjustTimes || 0;
          if (count >= adjustTimes) {
            currentTotalKeys += 1;
          }
        });
        if (currentTotalKeys >= 7) {
          setPressStatus('pressover');
        }
      }
    }
  }, [buttonDisable, data.keycaps]);

  return (
    <>
      <div className='keyboard_professional'>
        <div style={{width: '576px'}}>
          {/* <Steps current={stepNumber} items={steps} /> */}
        </div>
        <div className='keytestborad-professional-content'>
          <HandleLayout pid={deviceProductId} showBackground={false} style={{marginTop: '-74px'}} />
          <div style={{height: '42px', width: '100%'}}>
            <ConfigProvider theme={{
              components: {
                Alert: {
                  contentHeight: 42,
                  colorInfoBg: 'var(--bs-primary)',
                  colorInfo: '#FFFFFF'
                },
              },
            }}>
              {
                pressStatus === 'over' && (
                  <Alert
                    message={t('keytest_tab.content_alert_completed')}
                    type="info"
                    showIcon
                    closable
                    style={{width: '100%'}}
                  />
                )
              }
            </ConfigProvider>
          </div>
          <div className='keytest-keycap-button'>
            {pressStatus === 'none' &&
              <Button type="primary" onClick={handleStartButton}>{t('keytest_tab.start_calibration')}</Button>
            }
            {pressStatus === 'start' &&
              <Button type="primary" disabled>{t('keytest_tab.save_calibration')}</Button>
            }
            {pressStatus === 'pressover' &&
              <Button type="primary" onClick={handleSaveButton}>{t('keytest_tab.save_calibration')}</Button>
            }
            {pressStatus === 'over' &&
              <Button type="primary" disabled>{t('keytest_tab.save_calibration')}</Button>
            }
          </div>
        </div>
      </div>

      <div className='keyboard_professional-note'>
        <div className='keyboard_professional-content'>
          <div className='keyboard_professional-text'>
            <InfoCircleFilled style={{color: '#D7D7DB', fontSize: '15px'}} />
            <span>{t('keytest_tab.steps_content_title')}</span>
          </div>
          <div className='keyboard_professional-steps'>
            {itemPage === 'fast' &&
                <span>{t('keytest_tab.steps_content_fast_main')}
                </span> }
            {itemPage === 'professional' &&
                <span>{t('keytest_tab.steps_content_main')}
                </span> }
            <span className='text-steps-item'>{t('keytest_tab.steps_content_note1')}</span>
            {itemPage === 'professional' &&
            <span className='text-steps-item'>{t('keytest_tab.steps_content_note2')}</span>}
            {itemPage === 'fast' &&
            <span className='text-steps-item'>{t('keytest_tab.steps_content_fast_note2')}</span>}
            <span className='text-steps-item'>{t('keytest_tab.steps_content_note3')}</span>
          </div>
        </div>
      </div>
    </>
  );
}

export default KeyboardTest;