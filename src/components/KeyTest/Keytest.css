.keytest-content {
  display: flex;
  padding: 60px 24px;
  height: calc(100vh - 120px);
  flex-direction: column;
  align-items: center;
  gap: 28px;
  flex: 1 0 0;
  align-self: stretch;
  border-radius: 16px;
}

.keytest-tabs {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  gap: 24px;
}

.keytest-keyboard {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 48px;
}

.operation-panel {
  display: flex;
  width: 1180px;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.keytest-tab-button {
  display: flex;
  padding: 6px 40px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border: none;
  background: none;
  border-radius: 0;
  border-bottom: 2.5px solid var(--netural-netural-15, #262626);
}

.keytest-tab-button .button-item {
  color: var(--text-text-1, #D7D7DB);
  text-align: center;
  font-family: "vivo Sans";
  font-size: 16px;
  font-style: normal;
  font-weight: 650;
  line-height: 22px;
  /* 137.5% */
}

.keytest-tab-button:hover {
  display: flex;
  padding: 6px 40px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border: none;
  background: none;
  border-radius: 0;
  box-shadow: none;
  border-bottom: 2.5px solid var(--bs-primary);
}

.keytest-tab-button::after {
  display: flex;
  padding: 6px 40px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border: none;
  background: none;
  border-radius: 0;
  border-bottom: 2.5px solid var(--bs-primary);
}

.keytest-tab-button:active {
  display: flex;
  padding: 6px 40px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border: none;
  background: none;
  border-radius: 0;
  box-shadow: none;
  transition: none;
  border-bottom: 2.5px solid var(--bs-primary);
}

.keytest-tab-button:focus-visible {
  outline: none;
  transition: none;
}

.keytest-tab-button.active {
  display: flex;
  padding: 6px 40px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border: none;
  background: none;
  border-radius: 0;
  box-shadow: none;
  border-bottom: 2.5px solid var(--bs-primary);
}

.keytest-tab-button span {
  color: var(--text-text-1, #D7D7DB);
  text-align: center;
  font-family: "vivo Sans";
  font-size: 16px;
  font-style: normal;
  font-weight: 650;
  line-height: 22px;
  /* 137.5% */
}

.keybroad-groups {
  display: flex;
  width: 1180px;
  height: 308px;
  gap: 24px;
  flex-wrap: wrap;
  flex-direction: column-reverse;
}

.keybroad-main {
  width: 776px;
  height: 308px;
  flex-shrink: 0;
  gap: 3px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}

.keybroad-middle {
  width: 152px;
  height: 308px;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: space-between;
  justify-content: space-between;
}

.keybroad-number {
  width: 204px;
  height: 256px;
  flex-shrink: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
  flex-direction: row;
}

.keybroad-button {
  display: flex;
  width: 48px;
  height: 48px;
  /* padding: 17px 0px; */
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  aspect-ratio: 1/1;
  border-radius: 2px;
  border: 1px solid var(--stroke-stroke-42, rgba(255, 255, 255, 0.00));
  background: #48484e;
  outline: none;
}

.keybroad-button.pressed {
  border-radius: 2px;
  border: 1px solid var(--stroke-stroke-42, rgba(255, 255, 255, 0.00));
  background: #252528;
}

.keybroad-button.active {
  border-radius: 2px;
  border: 1px solid var(--stroke-stroke-42, rgba(255, 255, 255, 0.00));
  background: #444c5d
}

.keybroad-button-icon {
  width: 36px;
  height: 36px;
  line-height: 36px;
  background-size: cover;
  background-position: center;
  background: rgb(70 70 76);
}

.keybroad-button.pressed .keybroad-button-icon {
  background: rgb(35 35 38);
}

.keybroad-button.active .keybroad-button-icon {
  background: rgb(66, 74, 91)
}

.nondeclass .keybroad-button-icon {
  background: none;
  border: none;
}

.three-number-keycap {
  display: flex;
  flex-direction: row;
  flex: 1;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: space-around;
}

.last-col-keycap {
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  /* justify-content: space-between;
    align-content: space-around; */
  justify-content: space-around;
}

.nondeclass {
  background: none;
  border: none;
}

.operation-panel-menus {
  display: flex;
  align-items: center;
  gap: 27px;
  align-self: stretch;
}

.operation-panel-menu {
  display: flex;
  align-items: center;
  gap: 27px;
}

.operation-panel-alert {
  display: flex;
  padding: 9px 16px;
  align-items: flex-start;
  gap: 10px;
  flex: 1 0 0;
  border-radius: 4px;
}

.operation-panel-groups {
  display: flex;
  width: 316px;
  height: 24px;
  gap: 30px
}

.operation-panel-group {
  display: inline-flex;
  align-items: center;
  gap: 12px;
}

.operation-panel-group-button {
  display: flex;
  width: 24px;
  height: 24px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  aspect-ratio: 1/1;
  border-radius: 4px;
}

.operation-panel-group span {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.operation-panel-group-button.unpress {
  border-radius: 4px;
  border: 1px solid var(--stroke-stroke-42, rgba(255, 255, 255, 0.00));
  background: linear-gradient(0deg, var(--fill-new-fill-new-fill-43, rgba(235, 237, 250, 0.15)) 0%, var(--fill-new-fill-new-fill-43, rgba(235, 237, 250, 0.15)) 100%), var(--special-special-button-2, #2C2C30);
}

.operation-panel-group-button.active {
  border-radius: 4px;
  border: 1px solid var(--stroke-stroke-42, rgba(255, 255, 255, 0.00));
  background: linear-gradient(0deg, rgba(25, 107, 229, 0.10) 0%, rgba(25, 107, 229, 0.10) 100%), linear-gradient(0deg, var(--fill-new-fill-new-fill-43, rgba(235, 237, 250, 0.15)) 0%, var(--fill-new-fill-new-fill-43, rgba(235, 237, 250, 0.15)) 100%), var(--special-special-button-2, #2C2C30);
}

.operation-panel-group-button.pressed {
  border-radius: 4px;
  border: 1px solid var(--stroke-stroke-42, rgba(255, 255, 255, 0.00));
  background: linear-gradient(0deg, var(--fill-new-fill-new-fill-5, rgba(29, 30, 32, 0.50)) 0%, var(--fill-new-fill-new-fill-5, rgba(29, 30, 32, 0.50)) 100%), var(--special-special-button-2, #2C2C30);
}

.operation-panel-content {
  display: flex;
  align-items: center;
  gap: 10px;
  align-self: stretch;
}

.operation-panel-icon {
  display: flex;
  padding: 2px;
  align-items: flex-start;
  gap: 10px;
}

.operation-panel-content span {
  color: var(--text-text-1, #D7D7DB);
  font-family: "vivo Sans";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.operation-panel-divider {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;
}

.operation-panel-divider-line {
  height: 0px;
  align-self: stretch;
  stroke-width: 1px;
  stroke: var(--Stroke-Divider, rgba(255, 255, 255, 0.10));
}

.operation-panel-logs {
  display: flex;
  align-items: flex-start;
  gap: 108px;
  align-self: stretch;
}

.operation-panel-logs-divider {
  display: flex;
  width: 0px;
  align-items: center;
  gap: 10px;
  align-self: stretch;
}

.operation-panel-logs-main {
  display: flex;
  align-items: flex-start;
  gap: 26px;
}

.operation-panel-logs-apm {
  display: flex;
  align-items: flex-start;
  gap: 18px;
}

.operation-panel-logs-left,
.operation-panel-logs-right {
  display: flex;
  width: 392px;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}

.operation-panel-logs-show {
  display: flex;
  width: 392px;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}

.operation-panel-log-timer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  align-self: stretch;
  color: var(--text-text-3, rgba(215, 215, 219, 0.40));
  text-align: right;
  font-family: MiSans;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.operation-panel-log-keycap {
  display: flex;
  align-items: center;
  gap: 24px;
  align-self: stretch;
}

.operation-panel-log-keycap-icon {
  display: flex;
  align-items: center;
  gap: 22px;
  flex: 1 0 0;
}

.log-keycap-text {
  display: flex;
  padding: 0px 10px;
  /* justify-content: center; */
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  align-self: stretch;
}

.log-keycap-text-icon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: var(---HitBox, rgba(255, 255, 255, 0.00));
}

.log-keycap-icon {
  display: flex;
  width: 36px;
  height: 36px;
  /* padding: 11px 12px; */
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 2px;
  background: var(--special-special-button-2, #2C2C30);
}

.log-keycap-icon-svg {
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-size: cover;
  align-items: center;
  width: 100%;
  height: 36px;
  background: var(---HitBox, rgba(255, 255, 255, 0.00));
}

.log-keycap-text span {
  color: var(--text-text-3, rgba(215, 215, 219, 0.40));
  font-family: "vivo Sans";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.operation-panel-apm {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
}

.operation-panel-apm-group {
  width: 118.154px;
  height: 56.309px;
}

.operation-panel-apm span {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  color: var(--netural-netural-85, #D9D9D9);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.operation-panel-apm-label {
  display: flex;
  align-items: center;
  gap: 6px;
  align-self: stretch;
}

.operation-panel-apm-name {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  color: var(--netural-netural-85, #D9D9D9);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.operation-panel-apm-value {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  flex: 1 0 0;
  color: var(--netural-netural-85, #D9D9D9);
  font-family: "vivo Sans";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 137.5% */
}

.keyboard_professional {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* gap: 48px; */
  align-self: stretch;
}

.steps_items {
  display: flex;
  padding: 0px 800px;
  justify-content: center;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
}

.steps_item {
  display: flex;
  height: 24px;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;

  .steps_item-icon {
    width: 24px;
    height: 24px;
    border-radius: 32px;
    display: flex;
  }

  .steps_item-icon-rectangle {
    display: flex;
    width: 24px;
    height: 24px;
    flex-shrink: 0;
    border-radius: 32px;
    border-radius: 32px;
    border: 1px solid var(--text-text-3, rgba(215, 215, 219, 0.40));
    align-items: center;
    justify-content: center;
  }

  .steps_item-icon-rectangle.active {
    border-radius: 32px;
    border: 1px solid var(--bs-primary);
  }

  .steps_item-icon span {
    color: var(--text-text-3, rgba(215, 215, 219, 0.40));
    font-family: vivoSans-Regular;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    /* 150% */
  }

  .steps_item-title {
    display: flex;
    color: var(--text-text-3, rgba(215, 215, 219, 0.40));
    font-family: vivoSans-Regular;
    font-size: 16px;
    font-style: normal;
    white-space: nowrap;
    font-weight: 400;
    line-height: 24px;
    /* 150% */
  }
}

.steps_item.active {
  .steps_item-icon-rectangle {
    border: 1px solid var(--bs-primary);
    background: var(--bs-primary);
  }

  .steps_item-icon span {
    color: var(--Neutral-1, #FFF);
  }

  .steps_item-title {
    color: var(--netural-netural-85, #D9D9D9);
  }
}

.steps_item-tail {
  display: flex;
  padding-left: 4px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex: 1 0 0;
  height: 1px;
  width: 136px;
  background-image: url('../../assets/icons/keytest/numbers/line.svg');
  background-size: cover;
}

.steps_item-tail.active {
  background-image: url('../../assets/icons/keytest/numbers/lined.svg');
}

.keyboard_professional-note {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
}

.keyboard_professional-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 24px;
}

.keyboard_professional-text {
  display: flex;
  padding: 4px 8px;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  background: var(--fill-new-fill-new-fill-4, rgba(235, 237, 250, 0.15));
}

.keyboard_professional-content-icon {
  display: flex;
  padding: 2px;
  align-items: flex-start;
  gap: 10px;
}

.keyboard_professional-text span {
  color: var(--text-text-2, rgba(215, 215, 219, 0.60));
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.keyboard_professional-steps {
  display: flex;
  padding: 0px 8px;
  gap: 10px;
  align-self: stretch;
  flex-direction: column;
}

.keyboard_professional-steps span {
  color: var(--text-text-1, #D7D7DB);
  font-size: 15px;
  font-style: normal;
  font-weight: 650;
  line-height: 28px;
  /* 186.667% */

}

.keyboard_professional-steps span.text-steps-item {
  color: var(--text-text-1, #D7D7DB);
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 36px;
}

.keytestborad-professional-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 67px;
}

.keytest-keyborad-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 48px;
}

.keytest-keycap-button {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.keytest-keycap-button button,
.keytest-keyboard-button {
  display: flex;
  padding: 6px 40px;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.keytest-keycap-button.finish-calibration {
  border-radius: 2px;
  border: 1.5px solid var(--brand-brand-4, #6EB1FF) !important;
  background: linear-gradient(0deg, rgba(64, 160, 255, 0.15) 0%, rgba(64, 160, 255, 0.15) 100%), linear-gradient(0deg, var(--fill-new-fill-new-fill-43, rgba(235, 237, 250, 0.15)) 0%, var(--fill-new-fill-new-fill-43, rgba(235, 237, 250, 0.15)) 100%), var(--special-special-button-2, #2C2C30) !important;
}

.keytest-keycap-button button span {
  /* color: var(--character-primary-inverse, #FFF); */
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 137.5% */
}

.keytest-keycap-button button.active span {
  color: var(--character-primary-inverse, #FFF);
}

.keytest-keycap-button button.keytest-button-allow,
.keytest-keycap-button button.active {
  border-radius: 6px;
  border: 1px solid var(--bs-primary);
  background: var(--bs-primary);
  box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.04);
}

.keytest-keycap-button button.disable,
.keytest-keycap-button button.keytest-button-notallow {
  border-radius: 6px;
  border: 1px solid var(--bs-primary);
  background: linear-gradient(0deg, var(--fill-new-fill-new-fill-5, rgba(29, 30, 32, 0.50)) 0%, var(--fill-new-fill-new-fill-5, rgba(29, 30, 32, 0.50)) 100%), var(--bs-primary);
}

.keytest-keycap-button button.disable span {
  color: var(--bs-primary);
}

.keytest-keyborad-content-alert {
  display: flex;
  width: 776px;
  padding: 6px 16px;
  align-items: flex-start;
  gap: 10px;
  border-radius: 4px;
  background: var(--bs-primary);
}

.keytest-keyborad-alert-text {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1 0 0;
  align-self: stretch;
  justify-content: space-between;
}

.keyboard_professional-alert-icon {
  display: flex;
  padding: 2px;
  align-items: flex-start;
  gap: 10px;
}

.keytest-keyborad-alert-text span {
  color: #FFF;
  font-family: "vivo Sans";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.keyboard_professional-alert-close-icon {
  display: flex;
  padding: 2px;
  align-items: flex-start;
  gap: 10px;
}

.keyboard_professional-alert-span {
  width: 100%;
}

.keytest-keyborad-groups {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 48px;

}

.keytest-keyborad-group {
  display: flex;
  width: 776px;
  height: 256px;
  flex-shrink: 0;
  gap: 2px;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}

.keytest-keyborad-group button {
  display: flex;
  height: 48px;
  width: 48px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border-radius: 2px;
  border: 1px solid var(--stroke-stroke-42, rgba(255, 255, 255, 0.00));
  background: linear-gradient(0deg, var(--fill-new-fill-new-fill-5, rgba(29, 30, 32, 0.50)) 0%, var(--fill-new-fill-new-fill-5, rgba(29, 30, 32, 0.50)) 100%), var(--special-special-button-2, #2C2C30);
}

.keytest-keyborad-group button.active {
  border-radius: 2px;
  border: 1px solid var(--stroke-stroke-42, rgba(255, 255, 255, 0.00));
  background: linear-gradient(0deg, var(--fill-new-fill-new-fill-43, rgba(235, 237, 250, 0.15)) 0%, var(--fill-new-fill-new-fill-43, rgba(235, 237, 250, 0.15)) 100%), var(--special-special-button-2, #2C2C30);
}

.keytest-keyborad-group button.nondeclass {
  background: none;
  border: none;
}

.keytest-keycap-button-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;
}

.keytest-keycap-button-item-text {
  display: flex;
  width: 40px;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.keytest-keycap-button-item-spans {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.keytest-keycap-button-item-spans span {
  color: rgba(215, 215, 219, 0.4);
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px;
  /* 109.091% */
}

.keytest-keycap-button.active .keytest-keycap-button-item-spans span {
  color: #d7d7db;
}

.keytest-keycap-button.active .keytest-keycap-button-item-spans span.little {
  opacity: 1;
  color: #818287;
}

.keytest-keycap-button-item-spans span.little {
  opacity: 0.25;
  color: #D9D9D9;
  font-family: "vivo Sans";
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px;
}

.keytest-keycap-button-start {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
}


.ant-tabs-tab-btn {
  font-size: 16px;
  font-family: "vivo Sans";
}

.ant-tabs-tab-btn:focus-visible,
.ant-tabs-tab-remove:focus-visible {
  box-shadow: none !important;
  outline: none !important;
}