import { getPositionByNumber } from '../../utils/hidUtils';

const getKeyVoltages = (hexArray) => {
  // 提取14组电压数据，每组2字节
  return Array.from({ length: 14 }, (_, i) => [
    hexArray[4 + i * 2],
    hexArray[5 + i * 2]
  ]);
};

const handleKeyVoltages = (hexArray, updateKeycap, deviceProductId, field) => {
  const keyVoltages = getKeyVoltages(hexArray);
  const start_position = parseInt(`${hexArray[1]}${hexArray[2]}`, 16) / 2;

  keyVoltages.forEach((keyVoltage, index) => {
    const { layer, keyPosition } = getPositionByNumber(start_position + index, deviceProductId);
    if (keyPosition) {
      const [row, column] = keyPosition;
      updateKeycap(row, column, { [field]: `${keyVoltage[0]}${keyVoltage[1]}` }, layer);
    }
  });
};

const SetKeyVoltage = (hexArray, updateKeycap, deviceProductId) => {
  if (hexArray[0] === '4D') {
    handleKeyVoltages(hexArray, updateKeycap, deviceProductId, 'maxKeyVoltage');
  } else if (hexArray[0] === '4E') {
    handleKeyVoltages(hexArray, updateKeycap, deviceProductId, 'minKeyVoltage');
  } else if (hexArray[0] === '1B') {
    handleKeyVoltages(hexArray, updateKeycap, deviceProductId, 'realtimeVoltage');
  }
};

export default SetKeyVoltage;
