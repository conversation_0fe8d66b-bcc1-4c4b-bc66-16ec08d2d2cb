const  keyTestDate = [
    {
      id: 0,
      meth: 'KC_ESCAPE',
      item: 'Esc',
      key_value: 'Escape',
      station: 'main',
      col: ['00','00','00'],
      row: ['00','00','00'],
      ez_series: 'ez60-ez63-ez80' //按键位置在那个系列里面有
    },
    {
        id: 0.5,
        meth: '',
        item: '',
        station: 'main',
        width: '32px',
        ez_series: 'ez80'
    },
    {
        id: 1,
        meth: 'KC_F1',
        item: 'F1',
        key_value: 'F1',
        station: 'main',
        col: ['','','01'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 2,
        meth: 'KC_F2',
        item: 'F2',
        key_value: 'F2',
        station: 'main',
        col: ['','','02'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 3,
        meth: 'KC_F3',
        item: 'F3',
        key_value: 'F3',
        station: 'main',
        col: ['','','03'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 4,
        meth: 'KC_F4',
        item: 'F4',
        key_value: 'F4',
        station: 'main',
        col: ['','','04'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 4.1,
        meth: '',
        item: '',
        station: 'main',
        width: '32px',
        ez_series: 'ez80'
    },
    {
        id: 5,
        meth: 'KC_F5',
        item: 'F5',
        key_value: 'F5',
        station: 'main',
        col: ['','','05'],
        row: ['','','00'],
        ez_series: 'ez80'

    },
    {
        id: 6,
        meth: 'KC_F6',
        item: 'F6',
        key_value: 'F6',
        station: 'main',
        col: ['','','06'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 7,
        meth: 'KC_F7',
        item: 'F7',
        key_value: 'F7',
        station: 'main',
        col: ['','','07'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 8,
        meth: 'KC_F8',
        item: 'F8',
        key_value: 'F8',
        station: 'main',
        col: ['','','08'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 8.5,
        meth: '',
        item: '',
        station: 'main',
        width: '31.5px',
        ez_series: 'ez80'
    },
    {
        id: 9,
        meth: 'KC_F9',
        item: 'F9',
        key_value: 'F9',
        station: 'main',
        col: ['','','09'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 10,
        meth: 'KC_F10',
        item: 'F10',
        key_value: 'F10',
        station: 'main',
        col: ['','','0A'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 11,
        meth: 'KC_F11',
        item: 'F11',
        key_value: 'F11',
        station: 'main',
        col: ['','','0B'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 12,
        meth: 'KC_F12',
        item: 'F12',
        key_value: 'F12',
        station: 'main',
        col: ['','','0C'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 12.1,
        meth: '',
        item: '',
        width: '22px',
        ez_series: 'ez80'
    },
    {
        id: 13,
        meth: 'KC_PRINT_SCREEN',
        item: 'Prt',
        item1: 'Sc',
        key_value: 'PrintScreen',
        key_value_mac: 'F13',
        station: 'middle',
        col: ['','','0D'],
        row: ['','','00'],
        ez_series: 'ez80'
    },

    {
        id: 14,
        meth: 'KC_SCROLL_LOCK',
        item: 'Scr',
        item1: 'Lock',
        key_value: 'ScrollLock',
        station: 'middle',
        col: ['','','0E'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 15,
        meth: 'KC_PAUSE',
        item: 'Pause',
        key_value: 'Pause',
        station: 'middle',
        col: ['','','0F'],
        row: ['','','00'],
        ez_series: 'ez80'
    },
    {
        id: 16,
        meth: 'KC_INSERT',
        item: 'Insert',
        key_value: 'Insert',
        station: 'middle',
    },
    {
        id: 17,
        meth: 'KC_HOME',
        item: 'Home',
        key_value: 'Home',
        station: 'middle',
    },

    {
        id: 18,
        meth: 'KC_GRAVE',
        item: '~',
        item1: '`',
        key_value: 'Backquote',
        station: 'main',
        col: ['','','00'],
        row: ['','','01'],
        ez_series: 'ez80'
    },
    {
        id: 19,
        meth: 'KC_1',
        item: '!',
        item1: '1',
        key_value: 'Digit1',
        station: 'main',
        col: ['01','01','01'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80' //按键位置在那个系列里面有
    },
    {
        id: 20,
        meth: 'KC_2',
        item: '@',
        item1: '2',
        key_value: 'Digit2',
        station: 'main',
        col: ['02','02','02'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80' //按键位置在那个系列里面有
    },
    {
        id: 21,
        meth: 'KC_3',
        item: '#',
        item1: '3',
        key_value: 'Digit3',
        station: 'main',
        col: ['03','03','03'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80' //按键位置在那个系列里面有
    },
    {
        id: 22,
        meth: 'KC_4',
        item: '$',
        item1: '4',
        key_value: 'Digit4',
        station: 'main',
        col: ['04','04','04'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80' //按键位置在那个系列里面有
    },
    {
        id: 23,
        meth: 'KC_5',
        item: '%',
        item1: '5',
        key_value: 'Digit5',
        station: 'main',
        col: ['05','05','05'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80' //按键位置在那个系列里面有
    },
    {
        id: 24,
        meth: 'KC_6',
        item: '^',
        item1: '6',
        key_value: 'Digit6',
        station: 'main',
        col: ['06','06','06'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80' //按键位置在那个系列里面有
    },
    {
        id: 25,
        meth: 'KC_7',
        item: '&',
        item1: '7',
        key_value: 'Digit7',
        station: 'main',
        col: ['07','07','07'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80' //按键位置在那个系列里面有
    },
    {
        id: 26,
        meth: 'KC_8',
        item: '*',
        item1: '8',
        key_value: 'Digit8',
        station: 'main',
        col: ['08','08','08'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80' //按键位置在那个系列里面有
    },
    {
        id: 27,
        meth: 'KC_9',
        item: '(',
        item1: '9',
        key_value: 'Digit9',
        station: 'main',
        col: ['09','09','09'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80' //按键位置在那个系列里面有
    },
    {
        id: 28,
        meth: 'KC_0',
        item: ')',
        item1: '0',
        key_value: 'Digit0',
        station: 'main',
        col: ['0A','0A','0A'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 29,
        meth: 'KC_MINUS',
        item: '_',
        item1: '-',
        key_value: 'Minus',
        station: 'main',
        col: ['0B','0B','0B'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 30,
        meth: 'KC_EQUAL',
        item: '+',
        item1: '=',
        key_value: 'Equal',
        station: 'main',
        col: ['0C','0C','0C'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 31,
        meth: 'KC_BACKSPACE',
        item: 'Back',
        item1: 'Space',
        station: 'main',
        key_value: 'Backspace',
        width: '100px',
        col: ['0D','0D','0D'],
        row: ['00','00','01'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 31.1,
        meth: '',
        item: '',
        width: '22px',
        ez_series: 'ez80'
    },
    {
        id: 31.15,
        meth: 'KC_INSERT',
        item: 'Insert',
        key_value: 'Insert',
        col: ['','','0E'],
        row: ['','','01'],
        ez_series: 'ez80'
    },
    {
        id: 31.2,
        meth: 'KC_HOME',
        item: 'Home',
        key_value: 'Home',
        col: ['','','0F'],
        row: ['','','01'],
        ez_series: 'ez80'
    },
    {
        id: 31.3,
        meth: 'KC_PAGE_UP',
        item: 'Page',
        item1: 'Up',
        key_value: 'PageUp',
        col: ['','','10'],
        row: ['','','01'],
        ez_series: 'ez80'
    },
    {
        id: 32,
        meth: 'KC_KP_ENTER',
        item: 'Num Enter',
        key_value: 'NumLock',
        station: 'number'
    },
    {
        id: 33,
        meth: 'KC_KP_SLASH',
        item: '/',
        key_value: 'NumpadDivide',
        station: 'number'
    },
    {
        id: 34,
        meth: 'KC_KP_ASTERISK',
        item: '*',
        key_value: 'NumpadMultiply',
        station: 'number'
    },
    {
        id: 35,
        meth: 'KC_KP_MINUS',
        item: '-',
        key_value: 'NumpadSubtract',
        station: 'lnumber'
    },
    {
        id: 36,
        meth: 'KC_TAB',
        item: 'Tab',
        key_value: 'Tab',
        station: 'main',
        width: '72px',
        col: ['00','00','00'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 37,
        meth: 'KC_Q',
        item: 'Q',
        key_value: 'KeyQ',
        station: 'main',
        col: ['01','01','01'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 38,
        meth: 'KC_W',
        item: 'W',
        key_value: 'KeyW',
        station: 'main',
        col: ['02','02','02'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 39,
        meth: 'KC_E',
        item: 'E',
        key_value: 'KeyE',
        station: 'main',
        col: ['03','03','03'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 40,
        meth: 'KC_R',
        item: 'R',
        key_value: 'KeyR',
        station: 'main',
        col: ['04','04','04'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 41,
        meth: 'KC_T',
        item: 'T',
        key_value: 'KeyT',
        station: 'main',
        col: ['05','05','05'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 42,
        meth: 'KC_Y',
        item: 'Y',
        key_value: 'KeyY',
        station: 'main',
        col: ['06','06','06'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 43,
        meth: 'KC_U',
        item: 'U',
        key_value: 'KeyU',
        station: 'main',
        col: ['07','07','07'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 44,
        meth: 'KC_I',
        item: 'I',
        key_value: 'KeyI',
        station: 'main',
        col: ['08','08','08'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 45,
        meth: 'KC_O',
        item: 'O',
        key_value: 'KeyO',
        station: 'main',
        col: ['09','09','09'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 46,
        meth: 'KC_P',
        item: 'P',
        key_value: 'KeyP',
        station: 'main',
        col: ['0A','0A','0A'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },

    {
        id: 47,
        meth: 'KC_LEFT_BRACKET',
        item: '{',
        item1: '[',
        key_value: 'BracketLeft',
        station: 'main',
        col: ['0B','0B','0B'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 48,
        meth: 'KC_RIGHT_BRACKET',
        item: '}',
        item1: ']',
        key_value: 'BracketRight',
        station: 'main',
        col: ['0C','0C','0C'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 49,
        meth: 'KC_BACKSLASH',
        item: '|',
        item1: '\\',
        key_value: 'Backslash',
        station: 'main',
        width: '76px',
        col: ['0D','0D','0D'],
        row: ['01','01','02'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 49.05,
        meth: '',
        item: '',
        item1: '',
        width: '22px',
        ez_series: 'ez80'
    },
    {
        id: 49.1,
        meth: 'KC_DELETE',
        item: 'Delete',
        key_value: 'Delete',
        col: ['','','0E'],
        row: ['','','02'],
        ez_series: 'ez80'
    },
    {
        id: 49.2,
        meth: 'KC_END',
        item: 'End',
        key_value: 'End',
        col: ['','','0F'],
        row: ['','','02'],
        ez_series: 'ez80'
    },
    {
        id: 49.3,
        meth: 'KC_PAGE_DOWN',
        item: 'Page',
        item1: 'Down',
        key_value: 'PageDown',
        col: ['','','10'],
        row: ['','','02'],
        ez_series: 'ez80'
    },
    {
        id: 50,
        meth: 'KC_KP_7',
        item: '7',
        key_value: 'Numpad7',
        station: 'number'
    },
    {
        id: 51,
        meth: 'KC_KP_8',
        item: '8',
        key_value: 'Numpad8',
        station: 'number'
    },
    {
        id: 52,
        meth: 'KC_KP_9',
        item: '9',
        key_value: 'Numpad9',
        station: 'number'
    },
    {
        id: 53,
        meth: 'KC_KP_PLUS',
        item: '+',
        key_value: 'NumpadAdd',
        station: 'lnumber',
        heght: '100px'
    },
    {
        id: 54,
        meth: 'KC_CAPS_LOCK',
        item: 'Caps',
        item1: 'Lock',
        key_value: 'CapsLock',
        station: 'main',
        width: '86px',
        col: ['00','00','00'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },

    {
        id: 55,
        meth: 'KC_A',
        item: 'A',
        key_value: 'KeyA',
        station: 'main',
        col: ['01','01','01'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 56,
        meth: 'KC_S',
        item: 'S',
        key_value: 'KeyS',
        station: 'main',
        col: ['02','02','02'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 57,
        meth: 'KC_D',
        item: 'D',
        key_value: 'KeyD',
        station: 'main',
        col: ['03','03','03'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 58,
        meth: 'KC_F',
        item: 'F',
        key_value: 'KeyF',
        station: 'main',
        col: ['04','04','04'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 59,
        meth: 'KC_G',
        item: 'G',
        key_value: 'KeyG',
        station: 'main',
        col: ['05','05','05'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 60,
        meth: 'KC_H',
        item: 'H',
        key_value: 'KeyH',
        station: 'main',
        col: ['06','06','06'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 61,
        meth: 'KC_J',
        item: 'J',
        key_value: 'KeyJ',
        station: 'main',
        col: ['07','07','07'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 62,
        meth: 'KC_K',
        item: 'K',
        key_value: 'KeyK',
        station: 'main',
        col: ['08','08','08'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 63,
        meth: 'KC_L',
        item: 'L',
        key_value: 'KeyL',
        station: 'main',
        col: ['09','09','09'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 64,
        meth: 'KC_SEMICOLON',
        item: ':',
        item1: ';',
        key_value: 'Semicolon',
        station: 'main',
        col: ['0A','0A','0A'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 65,
        meth: 'KC_QUOTE',
        item: '\"',
        item1: '\'',
        key_value: 'Quote',
        station: 'main',
        col: ['0B','0B','0B'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 66,
        meth: 'KC_ENTER',
        item: 'Enter',
        station: 'main',
        key_value: 'Enter',
        width: '116px',
        col: ['0C','0C','0C'],
        row: ['02','02','03'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 66.12,
        meth: '',
        item: '',
        width: '22px',
        ez_series: 'ez80'
    },
    {
        id: 66.1,
        meth: '',
        item: '',
        station: '',
        key_value: '',
        ez_series: 'ez80'
    },
    {
        id: 66.2,
        meth: '',
        item: '',
        station: '',
        key_value: '',
        ez_series: 'ez80'
    },
    {
        id: 66.3,
        meth: '',
        item: '',
        station: '',
        key_value: '',
        ez_series: 'ez80'
    },

    {
        id: 67,
        meth: 'KC_PAGE_UP',
        item: 'Page',
        item1: 'Up',
        key_value: 'PageUp',
        station: 'middle'
    },
    {
        id: 68,
        meth: 'KC_KP_4',
        item: '4',
        key_value: 'Numpad4',
        station: 'number'
    },
    {
        id: 69,
        meth: 'KC_KP_5',
        item: '5',
        key_value: 'Numpad5',
        station: 'number'
    },
    {
        id: 70,
        meth: 'KC_KP_6',
        item: '6',
        key_value: 'Numpad6',
        station: 'number'
    },
    {
        id: 72,
        meth: 'KC_LEFT_SHIFT',
        item: 'L-Shift',
        key_value: 'ShiftLeft',
        station: 'main',
        width: '108px',
        col: ['00','00','00'],
        row: ['03','03','04'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 73,
        meth: 'KC_Z',
        item: 'Z',
        key_value: 'KeyZ',
        station: 'main',
        col: ['01','01','01'],
        row: ['03','03','04'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 74,
        meth: 'KC_X',
        item: 'X',
        key_value: 'KeyX',
        station: 'main',
        col: ['02','02','02'],
        row: ['03','03','04'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 75,
        meth: 'KC_C',
        item: 'C',
        key_value: 'KeyC',
        station: 'main',
        col: ['03','03','03'],
        row: ['03','03','04'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 76,
        meth: 'KC_V',
        item: 'V',
        key_value: 'KeyV',
        station: 'main',
        col: ['04','04','04'],
        row: ['03','03','04'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 77,
        meth: 'KC_B',
        item: 'B',
        key_value: 'KeyB',
        station: 'main',
        col: ['05','05','05'],
        row: ['03','03','04'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 78,
        meth: 'KC_N',
        item: 'N',
        key_value: 'KeyN',
        station: 'main',
        col: ['06','06','06'],
        row: ['03','03','04'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 79,
        meth: 'KC_M',
        item: 'M',
        key_value: 'KeyM',
        station: 'main',
        col: ['07','07','07'],
        row: ['03','03','04'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 80,
        meth: 'KC_COMMA',
        item: '<',
        item1: ',',
        key_value: 'Comma',
        station: 'main',
        col: ['08','08','08'],
        row: ['03','03','04'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 81,
        meth: 'KC_DOT',
        item: '>',
        item1: '.',
        key_value: 'Period',
        station: 'main',
        col: ['09','09','09'],
        row: ['03','03','04'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 82,
        meth: 'KC_SLASH',
        item: '?',
        item1: '/',
        key_value: 'Slash',
        station: 'main',
        col: ['0A','0A','0A'],
        row: ['03','03','04'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 83,
        meth: 'KC_DELETE',
        item: 'Delete',
        key_value: 'Delete',
        station: 'middle',
        col: ['','0D',''],
        row: ['','03',''],
        ez_series: 'ez63'
    },
    {
        id: 84,
        meth: 'KC_RIGHT_SHIFT',
        item: 'R-Shift',
        key_value: 'ShiftRight',
        station: 'main',
        width: '144px',
        col: ['0B','','0B'],
        row: ['03','','04'],
        ez_series: 'ez60-ez80'
    },

    {
        id: 84.1,
        meth: 'KC_RIGHT_SHIFT',//前面按键是按照这个来配置的
        item: 'R-Shift',
        key_value: 'ShiftRight',
        col: ['','0B',''],
        row: ['','03',''],
        ez_series: 'ez63'
    },
    {
        id: 84.2,
        meth: '',
        item: '',
        width: '22px',
        ez_series: 'ez80'
    },
    {
        id: 84.22,
        meth: '',
        item: '',
        ez_series: 'ez80'
    },
    {
        id: 84.3,
        meth: 'KC_UP',
        item: '↑',
        key_value: 'ArrowUp',
        col: ['','0C','0C'],
        row: ['','03','04'],
        ez_series: 'ez63-ez80'
    },
    {
        id: 84.4,
        meth: '',
        item: '',
        key_value: '',
        ez_series: 'ez80'
    },
    {
        id: 85,
        meth: 'KC_END',
        item: 'End',
        key_value: 'End',
        station: 'middle',
    },
    {
        id: 86,
        meth: 'KC_KP_1',
        item: '1',
        key_value: 'Numpad1',
        station: 'number'
    },
    {
        id: 87,
        meth: 'KC_KP_2',
        item: '2',
        key_value: 'Numpad2',
        station: 'number'
    },
    {
        id: 88,
        meth: 'KC_KP_3',
        item: '3',
        key_value: 'Numpad3',
        station: 'number'
    },
    {
        id: 89,
        meth: 'KC_KP_ENTER',
        item: 'Num',
        item1: 'Enter',
        key_value: 'NumpadEnter',
        station: 'lnumber',
        heght: '100px'
    },
    {
        id: 90,
        meth: 'KC_LEFT_CTRL',
        item: 'L-Ctrl',
        key_value: 'ControlLeft',
        station: 'main',
        width: '60px',
        col: ['00','','00'],
        row: ['04','','05'],
        ez_series: 'ez60-ez80'
    },
    {
        id: 90.1,
        meth: 'KC_LEFT_CTRL',
        item: 'L-Ctrl',
        key_value: 'ControlLeft',
        width: '60px',
        col: ['','00',''],
        row: ['','04',''],
        ez_series: 'ez63'
    },
    {
        id: 91,
        meth: 'KC_LEFT_GUI',
        item: 'L-Win',
        key_value: 'MetaLeft',
        station: 'main',
        width: '60px',
        col: ['01','','01'],
        row: ['04','','05'],
        ez_series: 'ez60-ez80'
    },
    {
        id: 91.1,
        meth: 'KC_LEFT_GUI',
        item: 'L-Win',
        key_value: 'MetaLeft',
        width: '60px',
        col: ['','01',''],
        row: ['','04',''],
        ez_series: 'ez63'
    },
    {
        id: 92,
        meth: 'KC_LEFT_ALT',
        item: 'L-Alt',
        key_value: 'AltLeft',
        station: 'main',
        width: '60px',
        col: ['02','','02'],
        row: ['04','','05'],
        ez_series: 'ez60-ez80'
    },
    {
        id: 92.1,
        meth: 'KC_LEFT_ALT',
        item: 'L-Alt',
        key_value: 'AltLeft',
        width: '60px',
        col: ['','02',''],
        row: ['','04',''],
        ez_series: 'ez63'
    },
    {
        id: 93,
        meth: 'KC_SPACE',
        item: 'Space',
        key_value: 'Space',
        station: 'main',
        width: '328px',
        col: ['03','03','03'],
        row: ['04','04','05'],
        ez_series: 'ez60-ez63-ez80'
    },
    {
        id: 94,
        meth: 'KC_RIGHT_ALT',
        item: 'R-Alt',
        key_value: 'AltRight',
        station: 'main',
        width: '60px',
        col: ['04','','04'],
        row: ['04','','05'],
        ez_series: 'ez60-ez80'
    },
    {
        id: 94.1,
        meth: 'KC_RIGHT_ALT',
        item: 'R-Alt',
        key_value: 'AltRight',
        col: ['','04',''],
        row: ['','04',''],
        ez_series: 'ez63'
    },
    {
        id: 95,
        meth: 'KC_RIGHT_GUI',
        item: 'R-Win',
        key_value: 'MetaRight',
        station: 'main',
        width: '60px',
        col: ['05','','05'],
        row: ['04','','05'],
        ez_series: 'ez60-ez80'
    },
    {
        id: 95.1,
        meth: 'KC_RIGHT_GUI',
        item: 'R-Win',
        key_value: 'MetaRight',
        col: ['','05',''],
        row: ['','04',''],
        ez_series: 'ez63'
    },
    {
        id: 96,
        meth: 'KC_MENU',
        item: 'Menu',
        key_value: 'Menu',
        station: 'main',
        width: '60px',
        col: ['06','','06'],
        row: ['04','','05'],
        ez_series: 'ez60-ez80'
    },
    {
        id: 97,
        meth: 'KC_RIGHT_CTRL',
        item: 'R-Ctrl',
        key_value: 'ControlRight',
        station: 'main',
        width: '60px',
        col: ['07','','07'],
        row: ['04','','05'],
        ez_series: 'ez60-ez80'
    },
    {
        id: 97.1,
        width: '22px',
        ez_series: 'ez80'
    },
    {
        id: 98,
        meth: 'KC_PAGE_DOWN',
        item: 'Page',
        item1: 'Down',
        key_value: 'PageDown',
        station: 'middle'

    },
    {
        id: 98.1,
        meth: '',
        item: '',
        station: 'middle'
    },
    {
        id: 98.2,
        meth: '',
        item: '',
        station: 'middle'
    },
    {
        id: 98.3,
        meth: '',
        item: '',
        station: 'middle'
    },
    {
        id: 98.4,
        meth: '',
        item: '',
        station: 'middle'
    },
    {
        id: 99,
        meth: 'KC_UP',
        item: '↑',
        key_value: 'ArrowUp',
        station: 'middle'
    },
    {
        id: 99.1,
        meth: '',
        item: '',
        station: 'middle'
    },
    {
        id: 100,
        meth: 'KC_LEFT',
        item: '←',
        key_value: 'ArrowLeft',
        station: 'middle',
        col: ['','06','08'],
        row: ['','04','05'],
        ez_series: 'ez63-ez80'
    },

    {
        id: 101,
        meth: 'KC_DOWN',
        item: '↓',
        key_value: 'ArrowDown',
        station: 'middle',
        col: ['','07','09'],
        row: ['','04','05'],
        ez_series: 'ez63-ez80'
    },
    {
        id: 102,
        meth: 'KC_RIGHT',
        item: '→',
        key_value: 'ArrowRight',
        station: 'middle',
        col: ['','08','0A'],
        row: ['','04','05'],
        ez_series: 'ez63-ez80'
    },
    {
        id: 103,
        meth: 'KC_KP_0',
        item: '0',
        item1: 'Ins',
        key_value: 'Numpad0',
        station: 'number',
        width: '100px'
    },
    {
        id: 104,
        meth: 'KC_KP_DOT',
        item: '.',
        item1: 'Del',
        key_value: 'NumpadDecimal',
        station: 'number'
    },
    {
        id: 105,
        meth: 'KC_NO',
        item: ' ',
        station: 'specil'
    },
    {
        id: 106,
        meth: 'KC_TRNS',
        item: '▽',
        station: 'specil'
    },
    {
        id: 107,
        meth: 'KC_SHIFT_AND_GRAVE',
        item: '~',
        station: 'specil'
    },
    {
        id: 108,
        meth: 'KC_SHIFT_AND_1',
        item: '!',
        station: 'specil'
    },
    {
        id: 109,
        meth: 'KC_SHIFT_AND_2',
        item: '@',
        station: 'specil'
    },
    {
        id: 110,
        meth: 'KC_SHIFT_AND_3',
        item: '#',
        station: 'specil'
    },
    {
        id: 111,
        meth: 'KC_SHIFT_AND_4',
        item: '$',
        station: 'specil'
    },
    {
        id: 112,
        meth: 'KC_SHIFT_AND_5',
        item: '%',
        station: 'specil'
    },
    {
        id: 113,
        meth: 'KC_SHIFT_AND_6',
        item: '^',
        station: 'specil'
    },
    {
        id: 114,
        meth: 'KC_SHIFT_AND_7',
        item: '&',
        station: 'specil'
    },
    {
        id: 115,
        meth: 'KC_SHIFT_AND_8',
        item: '*',
        station: 'specil'
    },
    {
        id: 115.5,
        meth: 'KC_KP_EQUAL',
        item: '=',
        station: 'specil'
    },
    {
        id: 116,
        meth: 'KC_SHIFT_AND_9',
        item: '(',
        station: 'specil'
    },
    {
        id: 117,
        meth: 'KC_SHIFT_AND_0',
        item: ')',
        station: 'specil'
    },
    {
        id: 118,
        meth: 'KC_SHIFT_AND_MINUS',
        item: '_',
        station: 'specil'
    },
    {
        id: 119,
        meth: 'KC_SHIFT_AND_EQUAL',
        item: '+',
        station: 'specil'
    },
    {
        id: 120,
        meth: 'KC_SHIFT_AND_LEFT_BRACKET',
        item: '{',
        station: 'specil'
    },
    {
        id: 121,
        meth: 'KC_SHIFT_AND_RIGHT_BRACKET',
        item: '}',
        station: 'specil'
    },
    {
        id: 122,
        meth: 'KC_SHIFT_AND_COMMA',
        item: '<',
        station: 'specil'
    },
    {
        id: 123,
        meth: 'KC_SHIFT_AND_PERIOD',
        item: '>',
        station: 'specil'
    },
    {
        id: 124,
        meth: 'KC_SHIFT_AND_SEMICOLON',
        item: ':',
        station: 'specil'
    },
    {
        id: 125,
        meth: 'KC_SHIFT_AND_BACKSLASH',
        item: '|',
        station: 'specil'
    },
    {
        id: 126,
        meth: 'KC_SHIFT_AND_SLASH',
        item: '?',
        station: 'specil'
    },
    {
        id: 127,
        meth: 'KC_SHIFT_AND_APOSTROPHE',
        item: '\"',
        station: 'specil'
    },
    {
        id: 128,
        meth: 'KC_KANA',
        item: 'かな',
        station: 'specil'
    },
    {
        id: 129,
        meth: 'KC_HENK',
        item: '變換',
        station: 'specil'
    },
    {
        id: 130,
        meth: 'KC_MHEN',
        item: '無變換',
        station: 'specil'
    },
    {
        id: 131,
        meth: 'KC_HAEN',
        item: '한영',
        station: 'specil'
    },
    {
        id: 132,
        meth: 'KC_HANJ',
        item: '漢字',
        station: 'specil'
    }
]



export default keyTestDate;