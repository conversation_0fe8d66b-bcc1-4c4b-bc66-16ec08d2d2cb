import processString from './processString' ;



const SvgIcon = ({ name, size = 36,changename = true, color = 'currentColor' }) => {
    // 封装的一个直接调用图片的方法，后续可以直接调用这个
    if(changename === true){
      name = processString(name,'picture')
    }
    const symbolId = `#icon-${name}`; // 需与 symbolId 配置一致

    return (

      <svg
        aria-hidden="true"
        width={size}
        height={size}
        fill={color}
      >
        <use href={symbolId} />
      </svg>
    );
  };

  export default SvgIcon;