// 通用替换规则
const COMMON_REPLACEMENTS = {
     '\\s+': '',
     'MO\\(1\\)': 'Fn1',
     'MO\\(0\\)': 'Fn0',

};

    // 图片类型特殊替换规则
const PICTURE_REPLACEMENTS = {
     ...COMMON_REPLACEMENTS,
     '\\\\': 'left_slash',
     '\\/': 'right_slash',
     '"': 'doublequotation',
     '\'': 'siglequotation',
     '\\.': 'period',
     '\\?': 'ques',
     '▿': 'downcontent',
     '\\#': 'well_no',
     '\\%': 'percent_sign',
     '\\(': 'left_parenthesis',
     '\\)': 'right_parenthesis',
     '\\{': 'l_big_parenthesis',
     '\\}': 'r_big_parenthesis',
     '\\>': 'greater_than',
     '\\<': 'less_than',
     '\\:': 'colon',
     '\\|': 'vertical_line',
};

// 其他类型替换规则
const OTHER_REPLACEMENTS = {
     ...COMMON_REPLACEMENTS,
      '\\/\\?': '/',
      '\\-_': '-',
      '\\=\\+': '=',
      '\\.Del': '.',
      '\\~\\.': '～',
      '\\"\'': '\''

};
/**
=== =  = *= 8使用替换规则对字符串进行处理
    * @param {string} str - 输入字符串
    * @param {Object} replacements - 替换规则对象
    * @returns {string} - 处理后的字符串
*/
const applyReplacements = (str, replacements) => {
    return Object.entries(replacements).reduce((result, [pattern, replacement]) => {
     return result.replace(new RegExp(pattern, 'g'), replacement);
     }, str);
};

/**
    * 处理字符串，根据内容类型应用不同的替换规则
    * @param {string} str - 输入字符串
    * @param {string} content_cate - 内容类型（'picture' 或其他）
    * @returns {string} - 处理后的字符串
     */
const processString = (str, content_cate) => {
     if (str === undefined) {
       return ' ';
     }
     let result = str;
     // if(content_cate === 'picture'){

     const replacements = content_cate === 'picture' ? PICTURE_REPLACEMENTS : OTHER_REPLACEMENTS;

     // 应用替换规则
     result = applyReplacements(result, replacements);

     // 处理数字后面的特殊符号
     const numberSpecialRegex = /^(\d+)([^a-zA-Z0-9_.]+)/;
     result = result.replace(
       numberSpecialRegex,
       content_cate === 'picture' ? '$1_' : '$1'
     );
    return result;
};

// 导出函数
export default processString;