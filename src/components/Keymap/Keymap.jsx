import React, { useState } from 'react';
import { Tabs } from 'antd';

import mouse_left from '../../assets/keyboards/mouse_left.svg';
import mouse_right from '../../assets/keyboards/mouse_right.svg';
import mouse_sliod_click from '../../assets/keyboards/mouse_sliod_click.svg';
import mouse_up_siod from '../../assets/keyboards/mouse_up_siod.svg';
import mouse_down_sliod from '../../assets/keyboards/mouse_down_sliod.svg';
import mouse_l_sliod from '../../assets/keyboards/mouse_l_sliod.svg';
import mouse_r_sliod from '../../assets/keyboards/mouse_r_sliod.svg';
import mouse_forward from '../../assets/keyboards/mouse_forward.svg';
import mouse_back from '../../assets/keyboards/mouse_back.svg';

import mult_music from '../../assets/keyboards/mult_music.png';
import mult_next from '../../assets/keyboards/mult_next.svg';
import mult_nomu from '../../assets/keyboards/mult_nomu.svg';
import mult_play from '../../assets/keyboards/mult_play.svg';
import mult_up from '../../assets/keyboards/mult_up.svg';
import mult_voice_down from '../../assets/keyboards/mult_voice_down.svg';
import mult_voice_up from '../../assets/keyboards/mult_voice_up.svg';

import lamp_effect_switch from '../../assets/keyboards/lamp_effect_switch.svg';
import lamp_bright_up from '../../assets/keyboards/lamp_bright_up.svg';
import lamp_bright_down from '../../assets/keyboards/lamp_bright_down.svg';
import lamp_speed_up from '../../assets/keyboards/lamp_speed_up.svg';
import lamp_speed_down from '../../assets/keyboards/lamp_speed_down.svg';
import lamp_effect_mode from '../../assets/keyboards/lamp_effect_mode.svg';

import layer_mode_a1 from '../../assets/keyboards/layer_mode_a1.svg';
import layer_mode_a2 from '../../assets/keyboards/layer_mode_a2.svg';
import layer_mode_a3 from '../../assets/keyboards/layer_mode_a3.svg';
import layer_mode_mo0 from '../../assets/keyboards/layer_mode_mo0.svg';
import layer_mode_mo1 from '../../assets/keyboards/layer_mode_mo1.svg';


import system_com_close from '../../assets/keyboards/system_com_close.svg';
import system_commac_close from '../../assets/keyboards/system_commac_close.svg';
import system_com_sleep from '../../assets/keyboards/system_com_sleep.svg';
import system_com_wake from '../../assets/keyboards/system_com_wake.svg';
import system_com_search from '../../assets/keyboards/system_com_search.svg';
import system_com_calc from '../../assets/keyboards/system_com_calc.svg';
import system_com_mail from '../../assets/keyboards/system_com_mail.svg';
import system_com_mycom from '../../assets/keyboards/system_com_mycom.svg';
import system_win_lock from '../../assets/keyboards/system_win_lock.svg';

import click_quick_socd from '../../assets/keyboards/click_quick_socd.png';

import { useTranslation } from 'react-i18next';


import './Keymap.css'

import keyDate from './keyDate.js'



function Keymap({ handleKeycapClick }) {
  const [activeTab, setActiveTab] = useState('basic');
  const { t } = useTranslation();
  const [activeKey, setActiveKey] = useState('-1');
  const keyboardStyle = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center'
  };
  const containerStyle = {
    display: 'flex',
    padding: '10px',
    marginTop: '40px',
    alignItems: 'center',
  };
  const keysetboardStyle = {
    width: '100px',
    hegth: '100px',
    backgroundColor: '#background: var(--3, #17181C)'
  };

  const items = [
    // {
    //   label: '欧式布局',
    //   key: '2',
    //   value: ""
    // }
  ];

  const kys_items = [['KC_ESCAPE', 'Esc'], []]

  const keyItem = keyDate.filter(i => i.id < 90);
  const lastkeyItem = keyDate.filter(i => i.id > 89 && i.id < 105);
  const specilkeyItem = keyDate.filter(i => i.id > 104);

  const mainItem = keyDate.filter(i => i.station == 'main');
  const middleItem = keyDate.filter(i => i.station == 'middle');
  const numberItem = keyDate.filter(i => i.station == 'number');
  const lnumberItem = keyDate.filter(i => i.station == 'lnumber');

  const menuProps = {
    items
  };

  const MouseList = [
    {
      id: 1,
      label: t('keymap.mousekey.mouse_left'),
      value: 'mouse_left',
      img: mouse_left,
      code: 'KC_MS_BTN1' //左键单击
    },
    {
      id: 2,
      label: t('keymap.mousekey.mouse_right'),
      value: 'mouse_right',
      img: mouse_right,
      code: 'KC_MS_BTN2' //右键单击
    },
    {
      id: 3,
      label: t('keymap.mousekey.mouse_sliod_click'),
      value: 'mouse_sliod_click',
      img: mouse_sliod_click,
      code: 'KC_MS_BTN3' //中间滚轮
    },
    {
      id: 6,
      label: t('keymap.mousekey.mouse_up_siod'),
      value: 'mouse_up_siod',
      img: mouse_up_siod,
      code: 'KC_MS_WH_UP'
    },

    {
      id: 7,
      label: t('keymap.mousekey.mouse_down_sliod'),
      value: 'mouse_down_sliod',
      img: mouse_down_sliod,
      code: 'KC_MS_WH_DOWN'
    },
    {
      id: 8,
      label: t('keymap.mousekey.mouse_l_sliod'),
      value: 'mouse_l_sliod',
      img: mouse_l_sliod,
      code: 'KC_MS_WH_LEFT'
    },
    {
      id: 9,
      label: t('keymap.mousekey.mouse_r_sliod'),
      value: 'mouse_r_sliod',
      img: mouse_r_sliod,
      code: 'KC_MS_WH_RIGHT'
    },
    {
      id: 10,
      label: t('keymap.mousekey.mouse_back'),
      value: 'mouse_back',
      img: mouse_back,
      code: 'KC_MS_BTN4'
    },
    {
      id: 11,
      label: t('keymap.mousekey.mouse_forward'),
      value: 'mouse_forward',
      img: mouse_forward,
      code: 'KC_MS_BTN5'
    },
  ]
  const MultimediaList = [
    {
      id: 1,
      label: t('keymap.multimediakey.mult_music'),
      value: 'mult_music',
      img: mult_music,
      code: 'KC_EJCT' //媒体播放器
    },
    {
      id: 2,
      label: t('keymap.multimediakey.mult_play'),
      value: 'mult_play',
      img: mult_play,
      code: 'KC_MPLY' //播放/暂停
    },
    {
      id: 3,
      label: t('keymap.multimediakey.mult_up'),
      value: 'mult_up',
      img: mult_up,
      code: 'KC_MPRV' //上一曲
    },
    {
      id: 4,
      label: t('keymap.multimediakey.mult_next'),
      value: 'mult_next',
      img: mult_next,
      code: 'KC_MNXT' //下一曲
    },
    {
      id: 5,
      label: t('keymap.multimediakey.mult_nomu'),
      value: 'mult_nomu',
      img: mult_nomu,
      code: 'KC_MUTE' //静音
    },


    {
      id: 6,
      label: t('keymap.multimediakey.mult_voice_down'),
      value: 'mult_voice_down',
      img: mult_voice_down,
      code: 'KC_VOLD' //音量-
    },
    {
      id: 7,
      label: t('keymap.multimediakey.mult_voice_up'),
      value: 'mult_voice_up',
      img: mult_voice_up,
      code: 'KC_VOLU' //音量+
    }
  ]
  const LightList = [
    {
      id: 1,
      label: t('keymap.lightkey.lamp_effect_switch'),
      value: 'lamp_effect_switch',
      img: lamp_effect_switch,
      code: 'BL_TOGG' //灯效开关
    },
    {
      id: 2,
      label: t('keymap.lightkey.lamp_bright_up'),
      value: 'lamp_bright_up',
      img: lamp_bright_up,
      code: 'RGB_VAI' //亮度+
    },
    {
      id: 3,
      label: t('keymap.lightkey.lamp_bright_down'),
      value: 'lamp_bright_down',
      img: lamp_bright_down,
      code: 'RGB_VAD' //亮度-
    },
    {
      id: 4,
      label: t('keymap.lightkey.lamp_speed_up'),
      value: 'lamp_speed_up',
      img: lamp_speed_up,
      code: 'RGB_SPI' //速度+
    },
    {
      id: 5,
      label: t('keymap.lightkey.lamp_speed_down'),
      value: 'lamp_speed_down',
      img: lamp_speed_down,
      code: 'RGB_SPD' //速度-
    },
    {
      id: 6,
      label: t('keymap.lightkey.lamp_effect_mode'),
      value: 'lamp_effect_mode',
      img: lamp_effect_mode,
      code: 'RGB_MOD' //灯效模式
    }
  ]

  const FclickList = [
    {
      id: 1,
      meth: 'KC_F13',
      item: 'F13'
    },
    {
      id: 2,
      meth: 'KC_F14',
      item: 'F14'
    },
    {
      id: 3,
      meth: 'KC_F15',
      item: 'F15'
    },
    {
      id: 4,
      meth: 'KC_F16',
      item: 'F16'
    },
    {
      id: 5,
      meth: 'KC_F17',
      item: 'F17'
    },
    {
      id: 6,
      meth: 'KC_F18',
      item: 'F18'
    },
    {
      id: 7,
      meth: 'KC_F19',
      item: 'F19'
    },
    {
      id: 8,
      meth: 'KC_F20',
      item: 'F20'
    },
    {
      id: 9,
      meth: 'KC_F21',
      item: 'F21'
    },
    {
      id: 10,
      meth: 'KC_F22',
      item: 'F22'
    },
    {
      id: 11,
      meth: 'KC_F23',
      item: 'F23'
    },
    {
      id: 12,
      meth: 'KC_F24',
      item: 'F24'
    }
  ]

  const SysclickList = [
    {
      id: 1,
      label: t('keymap.syskey.system_com_close'),
      value: 'system_com_close',
      img: system_com_close, //关机
      category: "",
      code: 'KC_PWR'
    },
    {
      id: 2,
      label: t('keymap.syskey.system_commac_close'),
      value: 'system_commac_close',
      img: system_commac_close, //关机
      category: "mac",
      code: 'KC_POWER'
    },
    {
      id: 3,
      label: t('keymap.syskey.system_com_sleep'),
      value: 'system_com_sleep',
      img: system_com_sleep, //休眠
      category: "",
      code: 'KC_SLEP'
    },
    {
      id: 4,
      label: t('keymap.syskey.system_com_wake'),
      value: 'system_com_wake',
      img: system_com_wake, //唤醒
      category: "",
      code: 'KC_WAKE'
    },
    {
      id: 5,
      label: t('keymap.syskey.system_com_search'),
      value: 'system_com_search',
      img: system_com_search, //搜索
      category: "",
      code: 'KC_WWW_SEARCH'
    },
    {
      id: 6,
      label: t('keymap.syskey.system_com_calc'),
      value: 'system_com_calc',
      img: system_com_calc, //打开计算器
      category: "",
      code: 'KC_CALC'
    },
    {
      id: 7,
      label: t('keymap.syskey.system_com_mail'),
      value: 'system_com_mail',
      img: system_com_mail, //打开邮箱
      category: "",
      code: 'KC_MAIL'
    },
    {
      id: 8,
      label: t('keymap.syskey.system_com_mycom'),
      value: 'system_com_mycom',
      img: system_com_mycom, //打开文件资源管理器
      category: "",
      code: 'KC_MYCM'
    },
    {
      id: 9,
      label: t('keymap.syskey.system_win_lock'),
      value: 'system_win_lock',
      img: system_win_lock, //打开文件资源管理器
      category: "",
      code: 'Win Lock'
    }
  ]

  const tabItems = [
    {
      key: 'basic',
      label: t('keymap.basic'),
    },
    {
      key: 'mouse',
      label: t('keymap.mouse'),
    },
    {
      key: 'multimedia',
      label: t('keymap.multimedia'),
    },
    {
      key: 'light',
      label: t('keymap.light'),
    },
    {
      key: 'layer',
      label: t('keymap.layer'),
    },
    {
      key: 'other',
      label: t('keymap.other'),
    },
    {
      key: 'macro',
      label: t('keymap.macro'),
    }
  ];

  const LayerList = [
    {
      id: 1,
      label: t('keymap.layerkey.layer_mode_a1'),
      value: 'layer_mode_a1',
      img: layer_mode_a1,
      categroy: 'mode',
      code: 'KC_A1' //灯效开关
    },
    {
      id: 2,
      label: t('keymap.layerkey.layer_mode_a2'),
      value: 'layer_mode_a2',
      img: layer_mode_a2,
      categroy: 'mode',
      code: 'KC_A2' //灯效开关
    },
    {
      id: 3,
      label: t('keymap.layerkey.layer_mode_a3'),
      value: 'layer_mode_a3',
      img: layer_mode_a3,
      categroy: 'mode',
      code: 'KC_A3' //灯效开关
    },
    {
      id: 4,
      label: t('keymap.layerkey.layer_mode_mo0'),
      value: 'layer_mode_mo0',
      img: layer_mode_mo0,
      categroy: 'click',
      code: 'KR_MOMENTARY_0' //灯效开关
    },
    {
      id: 5,
      label: t('keymap.layerkey.layer_mode_mo1'),
      value: 'layer_mode_mo1',
      img: layer_mode_mo1,
      categroy: 'click',
      code: 'KR_MOMENTARY_1' //灯效开关
    }
  ]
  const clickQick = [
    {
      id: 1,
      label: t('keymap.otherkey.click_quick_socd'),
      value: 'click_quick_socd',
      img: click_quick_socd,
      categroy: '',
      code: 'KC_SOCD_SWITCH' //灯效开关
    }
  ]

  const MacroList = [
    {
      id: 1,
      meth: 'KC_M0',
      item: 'M0'
    },
    {
      id: 2,
      meth: 'KC_M1',
      item: 'M1'
    },
    {
      id: 3,
      meth: 'KC_M2',
      item: 'M2'
    },
    {
      id: 4,
      meth: 'KC_M3',
      item: 'M3'
    },
    {
      id: 5,
      meth: 'KC_M4',
      item: 'M4'
    },
    {
      id: 6,
      meth: 'KC_M5',
      item: 'M5'
    },
    {
      id: 7,
      meth: 'KC_M6',
      item: 'M6'
    },
    {
      id: 8,
      meth: 'KC_M7',
      item: 'M7'
    },
    {
      id: 9,
      meth: 'KC_M8',
      item: 'M8'
    },
    {
      id: 10,
      meth: 'KC_M9',
      item: 'M9'
    },
    {
      id: 11,
      meth: 'KC_M10',
      item: 'M10'
    },
    {
      id: 12,
      meth: 'KC_M11',
      item: 'M11'
    },
    {
      id: 13,
      meth: 'KC_M12',
      item: 'M12'
    },
    {
      id: 14,
      meth: 'KC_M13',
      item: 'M13'
    },
    {
      id: 15,
      meth: 'KC_M14',
      item: 'M14'
    },
    {
      id: 16,
      meth: 'KC_M15',
      item: 'M15'
    }
  ]

  const changeTab = (key) => {
    setActiveTab(key);
  };

  return (
    <div style={keyboardStyle}>
      <div className='center-container'>
        <div className="header">
          <div className="header-left">
            <div className='title-cover'>
            </div>

            <div className='title'>
              <span>{t('keymap.clicktitle')}</span>
            </div>
          </div>
          <div className="header-right">
            <Tabs
              activeKey={activeTab}
              onChange={changeTab}
              items={tabItems}
              className="keymap-tabs"
              tabBarGutter={60}
            />
          </div>
        </div>
        <div className='keyboard-content'>
          <div className='keyboard-board' style={{ display: activeTab === 'basic' ? '' : 'none' }}>
            <div className="basic-keycap">
              <div className="main-keycap">
                {mainItem.map(keyitem => (
                  <div className={`keycap keycap-u1 keycap-umarge  ${keyitem.meth === '' ? 'nondeclass' : ''}`} style={{ width: keyitem.width }} key={keyitem.id} onClick={() => handleKeycapClick(keyitem.meth)}>
                    <div className={activeKey === 'key' + keyitem.id ? 'keycap-inside-bg active' : 'keycap-inside-bg'} onClick={() => setActiveKey('key' + keyitem.id)}>
                      {/* <div className='keycap-inside-bg'> */}
                      <span >{keyitem.item}</span>
                      {keyitem.item1 && <span>{keyitem.item1}</span>}
                    </div>
                  </div>))
                }
              </div>
              <div className="middle-keycap">
                {middleItem.map(keyitem => (
                  <div className={`keycap keycap-u1 keycap-umarge  ${keyitem.meth === '' ? 'nondeclass' : ''}`} style={{ width: keyitem.width }} key={keyitem.id} onClick={() => handleKeycapClick(keyitem.meth)}>
                    <div className={activeKey === 'key' + keyitem.id ? 'keycap-inside-bg active' : 'keycap-inside-bg'} onClick={() => setActiveKey('key' + keyitem.id)}>
                      {/* <div className='keycap-inside-bg'> */}
                      <span >{keyitem.item}</span>
                      {keyitem.item1 && <span>{keyitem.item1}</span>}
                    </div>
                  </div>))
                }
              </div>
              <div className="number-keycap">
                <div className="three-number-keycap">
                  {numberItem.map(keyitem => (
                    <div className={`keycap keycap-u1 keycap-umarge  ${keyitem.meth === '' ? 'nondeclass' : ''}`} style={{ width: keyitem.width }} key={keyitem.id} onClick={() => handleKeycapClick(keyitem.meth)}>
                      <div className={activeKey === 'key' + keyitem.id ? 'keycap-inside-bg active' : 'keycap-inside-bg'} onClick={() => setActiveKey('key' + keyitem.id)}>
                        {/* <div className='keycap-inside-bg'> */}
                        <span >{keyitem.item}</span>
                        {keyitem.item1 && <span>{keyitem.item1}</span>}
                      </div>
                    </div>))
                  }
                </div>
                <div className="last-col-keycap">
                  {lnumberItem.map(keyitem => (
                    <div className={`keycap keycap-u1 keycap-umarge  ${keyitem.meth === '' ? 'nondeclass' : ''}`} style={{ height: keyitem.heght }} key={keyitem.id} onClick={() => handleKeycapClick(keyitem.meth)}>
                      <div className={activeKey === 'key' + keyitem.id ? 'keycap-inside-bg active' : 'keycap-inside-bg'} onClick={() => setActiveKey('key' + keyitem.id)}>
                        {/* <div className='keycap-inside-bg'> */}
                        <span >{keyitem.item}</span>
                        {keyitem.item1 && <span>{keyitem.item1}</span>}
                      </div>
                    </div>))
                  }
                </div>

              </div>
            </div>
            <div className="specil-keycap">
              {specilkeyItem.map(keyitem => (
                <div className="keycap keycap-u1 keycap-umarge" key={keyitem.id} onClick={() => handleKeycapClick(keyitem.meth)}>
                  <div className={activeKey === 'key' + keyitem.id ? 'keycap-inside-bg active' : 'keycap-inside-bg'} onClick={() => setActiveKey('key' + keyitem.id)}>
                    {/* <div className='keycap-inside-bg'> */}
                    <span >{keyitem.item}</span>
                    {keyitem.item1 && <span>{keyitem.item1}</span>}
                  </div>
                </div>))}
            </div>
          </div>
          <div className='keyboard-mouse' style={{ display: activeTab === 'mouse' ? '' : 'none' }}>
            {MouseList.map(mousel => (
              <div className={activeKey === 'mouse' + mousel.id ? 'mouse-click active' : 'mouse-click'} key={mousel.id} onClick={() => { handleKeycapClick(mousel.code), setActiveKey('mouse' + mousel.id) }}>
                <div className='mouse-cover'>
                  <img src={mousel.img} alt={mousel.label} />
                </div>
                <div className='mouse-text'>
                  <span>{mousel.label}</span>
                </div>
              </div>
            ))}
          </div>
          <div className='keyboard-multimedia' style={{ display: activeTab === 'multimedia' ? '' : 'none' }}>
            {MultimediaList.map(multi => (
              <div className={activeKey === 'multimedia' + multi.id ? 'multimedia-click active' : 'multimedia-click'} key={multi.id} onClick={() => { handleKeycapClick(multi.code), setActiveKey('multimedia' + multi.id) }}>
                <div className='multimedia-cover'>
                  <img src={multi.img} alt={multi.label} />
                </div>
                <div className='multimedia-text'>
                  <span>{multi.label}</span>
                </div>
              </div>
            ))}
          </div>
          <div className='keyboard-multimedia' style={{ display: activeTab === 'light' ? '' : 'none' }}>
            {LightList.map(litem => (
              // <div className='multimedia-click' key={litem.id}  onClick={() => handleKeycapClick(litem.code)} >
              <div className={activeKey === 'light' + litem.id ? 'light-click active' : 'light-click'} key={litem.id} onClick={() => { handleKeycapClick(litem.code), setActiveKey('light' + litem.id) }}>
                <div className='multimedia-cover'>
                  <img src={litem.img} alt={litem.label} />
                </div>
                <div className='multimedia-text'>
                  <span>{litem.label}</span>
                </div>
              </div>
            ))}
          </div>
          <div className='keyboard-layer' style={{ display: activeTab === 'layer' ? '' : 'none' }}>
            <div className='layer-text-title'>
              <span>{t('keymap.press_sec_switch')}</span>
            </div>
            {LayerList.filter(item => item.categroy === 'mode').map(litem => (
              //  <div className='layer-click'  key={litem.id} onClick={() => handleKeycapClick(litem.code)} >
              <div className={activeKey === 'layer' + litem.id ? 'layer-click active' : 'layer-click'} key={litem.id} onClick={() => { handleKeycapClick(litem.code), setActiveKey('layer' + litem.id) }}>

                <div className='layer-cover'>
                  <img src={litem.img} alt={litem.label} />
                </div>
                <div className='multimedia-text'>
                  <span>{litem.label} </span>
                </div>
              </div>
            ))}

            <div className='layer-text-title'>
              <span>{t('keymap.click_cut_layer')}</span>
            </div>
            {LayerList.filter(item => item.categroy === 'click').map(litem => (
              // <div className='layer-click' key={litem.id} onClick={() => handleKeycapClick(litem.code)} >
              <div className={activeKey === 'layer' + litem.id ? 'layer-click active' : 'layer-click'} key={litem.id} onClick={() => { handleKeycapClick(litem.code), setActiveKey('layer' + litem.id) }}>
                <div className='layer-cover'>
                  <img src={litem.img} alt={litem.label} />
                </div>
                <div className='multimedia-text'>
                  <span>{litem.label}</span>
                </div>
              </div>
            ))}
          </div>
          <div className='keyboard-other-content' style={{ display: activeTab === 'other' ? '' : 'none' }}>
            <div className='keyboard-other'>
              <div className='other-text-title'>
                <span>{t('keymap.Fclick_title')}</span>
              </div>
              <div className="last-keycap">
                {FclickList.map(keyitem => (
                  <div className="keycap keycap-u1 keycap-umarge" key={keyitem.id} onClick={() => handleKeycapClick(keyitem.meth)}>
                    {/* <div  className={ activeKey === 'fkey' + keyitem.id ? 'keycap keycap-u1 keycap-umarge active' : 'keycap keycap-u1 keycap-umarge'}  key={keyitem.id} onClick={() => {handleKeycapClick(keyitem.meth),setActiveKey('fkey'+keyitem.id)}} > */}
                    <div className={activeKey === 'key' + keyitem.id ? 'keycap-inside-bg active' : 'keycap-inside-bg'} onClick={() => setActiveKey('key' + keyitem.id)}>

                      <span >{keyitem.item}</span>
                    </div>
                  </div>
                ))}
              </div>
              <div className='other-text-title'>
                <span>{t('keymap.system_title')}</span>
              </div>
              {SysclickList.filter(sitem => sitem.category !== 'mac').map(litem => (
                // <div className='other-click' key={litem.id} onClick={() => handleKeycapClick(litem.code)}>
                <div className={activeKey === 'other' + litem.id ? 'other-click active' : 'other-click'} key={litem.id} onClick={() => { handleKeycapClick(litem.code), setActiveKey('other' + litem.id) }}>

                  <div className='other-cover'>
                    <img src={litem.img} alt={litem.label} />
                  </div>
                  <div className='multimedia-text'>
                    <span>{litem.label}</span>
                  </div>
                </div>
              )
              )}
              <div className='other-text-title'>
                <span>{t('keymap.system_title_osx')}</span>
              </div>
              {SysclickList.filter(sitem => sitem.category === 'mac').map(litem => (
                // <div className='other-click' key={litem.id} onClick={() => handleKeycapClick(litem.code)}>
                <div className={activeKey === 'sother' + litem.id ? 'other-click active' : 'other-click'} key={litem.id} onClick={() => { handleKeycapClick(litem.code), setActiveKey('sother' + litem.id) }}>

                  <div className='other-cover'>
                    <img src={litem.img} alt={litem.label} />
                  </div>
                  <div className='multimedia-text'>
                    <span>{litem.label}</span>
                  </div>
                </div>
              ))}
              <div className='other-text-title'>
                <span>{t('keymap.click_quick_switch')}</span>
              </div>
              {clickQick.map(litem => (
                <div className={activeKey === 'qother' + litem.id ? 'other-click active' : 'other-click'} key={litem.id} onClick={() => { handleKeycapClick(litem.code), setActiveKey('qother' + litem.id) }}>
                  <div className='other-cover'>
                    <img className='other-click-cover' src={litem.img} alt={litem.label} />
                  </div>
                  <div className='multimedia-text'>
                    <span>{litem.label}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className='keyboard-other-content' style={{ display: activeTab === 'macro' ? '' : 'none' }}>
            <div className=''>
              <div className="last-keycap">
                {MacroList.map(keyitem => (
                  <div className="keycap keycap-u1 keycap-umarge" key={keyitem.id} onClick={() => handleKeycapClick(keyitem.meth)}>
                    {/* <div  className={ activeKey === 'fkey' + keyitem.id ? 'keycap keycap-u1 keycap-umarge active' : 'keycap keycap-u1 keycap-umarge'}  key={keyitem.id} onClick={() => {handleKeycapClick(keyitem.meth),setActiveKey('fkey'+keyitem.id)}} > */}
                    <div className={activeKey === 'key' + keyitem.id ? 'keycap-inside-bg active' : 'keycap-inside-bg'} onClick={() => setActiveKey('key' + keyitem.id)}>
                      <span >{keyitem.item}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Keymap;
