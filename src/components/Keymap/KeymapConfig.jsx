import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import Keymap from './Keymap';
import { KeyboardContext } from '../Keyboard/KeyboardContext';
import { useContext } from 'react';
import { findCodeByKey } from '../../utils/hidUtils';

const KeymapConfig = () => {
  const { addToQueue } = useHandleDevice();
  const { data } = useContext(KeyboardContext);

  const handleKeycapClick = (keycapName) => {
    addToQueue(`05 ${data.currentLayer} ${data.currentSelectedKey.row} ${data.currentSelectedKey.column} ${findCodeByKey(keycapName)} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
  };

  return (
    <div>
      <Keymap handleKeycapClick={handleKeycapClick} />
    </div>
  )
}

export default KeymapConfig;