import { findNameByCode } from '../../utils/hidUtils';

const SetKeymap = (hexArray, setCurrentLayer, updateKeycap) => {
  var layer = hexArray[1];
  var row = hexArray[2];
  var column = hexArray[3];
  var first_code = hexArray[4];
  var second_code = hexArray[5];

  const keyName = findNameByCode(`${first_code} ${second_code}`);
  updateKeycap(row, column, { label: keyName }, layer);
};

export default SetKeymap;