


const   keyDate = [
    {
      id: 0,
      meth: 'KC_ESCAPE',
      item: 'Esc',
      station: 'main'
    },
    {
        id: 0.5,
        meth: '',
        item: '',
        station: 'main',
        width: '32px'
    },
    {
        id: 1,
        meth: 'KC_F1',
        item: 'F1',
        station: 'main'
    },
    {
        id: 2,
        meth: 'KC_F2',
        item: 'F2',
        station: 'main'
    },
    {
        id: 3,
        meth: 'KC_F3',
        item: 'F3',
        station: 'main'
    },
    {
        id: 4,
        meth: 'KC_F4',
        item: 'F4',
        station: 'main'
    },
    {
        id: 4.1,
        meth: '',
        item: '',
        station: 'main',
        width: '32px'
    },
    {
        id: 5,
        meth: 'KC_F5',
        item: 'F5',
        station: 'main'
    },
    {
        id: 6,
        meth: 'KC_F6',
        item: 'F6',
        station: 'main'
    },
    {
        id: 7,
        meth: 'KC_F7',
        item: 'F7',
        station: 'main'
    },
    {
        id: 8,
        meth: 'KC_F8',
        item: 'F8',
        station: 'main'
    },
    {
        id: 8.5,
        meth: '',
        item: '',
        station: 'main',
        width: '31.5px'
    },
    {
        id: 9,
        meth: 'KC_F9',
        item: 'F9',
        station: 'main'
    },
    {
        id: 10,
        meth: 'KC_F10',
        item: 'F10',
        station: 'main'
    },
    {
        id: 11,
        meth: 'KC_F11',
        item: 'F11',
        station: 'main'
    },
    {
        id: 12,
        meth: 'KC_F12',
        item: 'F12',
        station: 'main'
    },
    {
        id: 13,
        meth: 'KC_PRINT_SCREEN',
        item: 'Prt',
        item1: 'Sc',
        station: 'middle'
    },
    {
        id: 14,
        meth: 'KC_SCROLL_LOCK',
        item: 'Scr',
        item1: 'Lock',
        station: 'middle'
    },
    {
        id: 15,
        meth: 'KC_PAUSE',
        item: 'Pause',
        station: 'middle'
    },
    // {
    //     id: 15.1,
    //     meth: '',
    //     item: '',
    //     station: 'middle'
    // },
    // {
    //     id: 15.2,
    //     meth: '',
    //     item: '',
    //     station: 'middle'
    // },
    // {
    //     id: 15.3,
    //     meth: '',
    //     item: '',
    //     station: 'middle'
    // },
    // {
    //     id: 15.4,
    //     meth: '',
    //     item: '',
    //     station: 'middle'
    // },
    {
        id: 16,
        meth: 'KC_INSERT',
        item: 'Insert',
        station: 'middle'
    },
    {
        id: 17,
        meth: 'KC_HOME',
        item: 'Home',
        station: 'middle'
    },
    {
        id: 18,
        meth: 'KC_GRAVE',
        item: '~',
        item1: '`',
        station: 'main'
    },
    {
        id: 19,
        meth: 'KC_1',
        item: '!',
        item1: '1',
        station: 'main'
    },
    {
        id: 20,
        meth: 'KC_2',
        item: '@',
        item1: '2',
        station: 'main'
    },
    {
        id: 21,
        meth: 'KC_3',
        item: '#',
        item1: '3',
        station: 'main'
    },
    {
        id: 22,
        meth: 'KC_4',
        item: '$',
        item1: '4',
        station: 'main'
    },
    {
        id: 23,
        meth: 'KC_5',
        item: '%',
        item1: '5',
        station: 'main'
    },
    {
        id: 24,
        meth: 'KC_6',
        item: '^',
        item1: '6',
        station: 'main'
    },
    {
        id: 25,
        meth: 'KC_7',
        item: '&',
        item1: '7',
        station: 'main'
    },
    {
        id: 26,
        meth: 'KC_8',
        item: '*',
        item1: '8',
        station: 'main'
    },
    {
        id: 27,
        meth: 'KC_9',
        item: '(',
        item1: '9',
        station: 'main'
    },
    {
        id: 28,
        meth: 'KC_0',
        item: ')',
        item1: '0',
        station: 'main'
    },
    {
        id: 29,
        meth: 'KC_MINUS',
        item: '_',
        item1: '-',
        station: 'main'
    },
    {
        id: 30,
        meth: 'KC_EQUAL',
        item: '+',
        item1: '=',
        station: 'main'
    },
    {
        id: 31,
        meth: 'KC_BACKSPACE',
        item: 'Back',
        item1: 'Space',
        station: 'main',
        width: '100px'
    },
    {
        id: 32,
        meth: 'KC_NUM_LOCK',
        item: 'Num Lock',
        station: 'number'
    },
    {
        id: 33,
        meth: 'KC_KP_SLASH',
        item: '/',
        station: 'number'
    },
    {
        id: 34,
        meth: 'KC_KP_ASTERISK',
        item: '*',
        station: 'number'
    },
    {
        id: 35,
        meth: 'KC_KP_MINUS',
        item: '-',
        station: 'lnumber'
    },
    {
        id: 36,
        meth: 'KC_TAB',
        item: 'Tab',
        station: 'main',
        width: '72px'
    },
    {
        id: 37,
        meth: 'KC_Q',
        item: 'Q',
        station: 'main'
    },
    {
        id: 38,
        meth: 'KC_W',
        item: 'W',
        station: 'main'
    },
    {
        id: 39,
        meth: 'KC_E',
        item: 'E',
        station: 'main'
    },
    {
        id: 40,
        meth: 'KC_R',
        item: 'R',
        station: 'main'
    },
    {
        id: 41,
        meth: 'KC_T',
        item: 'T',
        station: 'main'
    },
    {
        id: 42,
        meth: 'KC_Y',
        item: 'Y',
        station: 'main'
    },
    {
        id: 43,
        meth: 'KC_U',
        item: 'U',
        station: 'main'
    },
    {
        id: 44,
        meth: 'KC_I',
        item: 'I',
        station: 'main'
    },
    {
        id: 45,
        meth: 'KC_O',
        item: 'O',
        station: 'main'
    },
    {
        id: 46,
        meth: 'KC_P',
        item: 'P',
        station: 'main'
    },

    {
        id: 47,
        meth: 'KC_LEFT_BRACKET',
        item: '{',
        item1: '[',
        station: 'main'
    },
    {
        id: 48,
        meth: 'KC_RIGHT_BRACKET',
        item: '}',
        item1: ']',
        station: 'main'
    },
    {
        id: 49,
        meth: 'KC_BACKSLASH',
        item: '|',
        item1: '\\',
        station: 'main',
        width: '76px'
    },
    {
        id: 50,
        meth: 'KC_KP_7',
        item: '7',
        station: 'number'
    },
    {
        id: 51,
        meth: 'KC_KP_8',
        item: '8',
        station: 'number'
    },
    {
        id: 52,
        meth: 'KC_KP_9',
        item: '9',
        station: 'number'
    },
    {
        id: 53,
        meth: 'KC_KP_PLUS',
        item: '+',
        station: 'lnumber',
        heght: '100px'
    },
    {
        id: 54,
        meth: 'KC_CAPS_LOCK',
        item: 'Caps',
        item1: 'Lock',
        station: 'main',
        width: '86px'
    },

    {
        id: 55,
        meth: 'KC_A',
        item: 'A',
        station: 'main'
    },
    {
        id: 56,
        meth: 'KC_S',
        item: 'S',
        station: 'main'
    },
    {
        id: 57,
        meth: 'KC_D',
        item: 'D',
        station: 'main'
    },
    {
        id: 58,
        meth: 'KC_F',
        item: 'F',
        station: 'main'
    },
    {
        id: 59,
        meth: 'KC_G',
        item: 'G',
        station: 'main'
    },
    {
        id: 60,
        meth: 'KC_H',
        item: 'H',
        station: 'main'
    },
    {
        id: 61,
        meth: 'KC_J',
        item: 'J',
        station: 'main'
    },
    {
        id: 62,
        meth: 'KC_K',
        item: 'K',
        station: 'main'
    },
    {
        id: 63,
        meth: 'KC_L',
        item: 'L',
        station: 'main'
    },
    {
        id: 64,
        meth: 'KC_SEMICOLON',
        item: ':',
        item1: ';',
        station: 'main'
    },
    {
        id: 65,
        meth: 'KC_QUOTE',
        item: '\"',
        item1: '\'',
        station: 'main'
    },
    {
        id: 66,
        meth: 'KC_ENTER',
        item: 'Enter',
        station: 'main',
        width: '116px'
    },
    {
        id: 67,
        meth: 'KC_PAGE_UP',
        item: 'Page',
        item1: 'Up',
        station: 'middle'
    },
    {
        id: 68,
        meth: 'KC_KP_4',
        item: '4',
        station: 'number'
    },
    {
        id: 69,
        meth: 'KC_KP_5',
        item: '5',
        station: 'number'
    },
    {
        id: 70,
        meth: 'KC_KP_6',
        item: '6',
        station: 'number'
    },
    // {
    //     id: 71,
    //     meth: 'KC_KP_EQUAL',
    //     item: '=',
    //     station: 'number'
    // },
    {
        id: 72,
        meth: 'KC_LEFT_SHIFT',
        item: 'L-Shift',
        station: 'main',
        width: '108px'
    },
    {
        id: 73,
        meth: 'KC_Z',
        item: 'Z',
        station: 'main'
    },
    {
        id: 74,
        meth: 'KC_X',
        item: 'X',
        station: 'main'
    },
    {
        id: 75,
        meth: 'KC_C',
        item: 'C',
        station: 'main'
    },
    {
        id: 76,
        meth: 'KC_V',
        item: 'V',
        station: 'main'
    },
    {
        id: 77,
        meth: 'KC_B',
        item: 'B',
        station: 'main'
    },
    {
        id: 78,
        meth: 'KC_N',
        item: 'N',
        station: 'main'
    },
    {
        id: 79,
        meth: 'KC_M',
        item: 'M',
        station: 'main'
    },
    {
        id: 80,
        meth: 'KC_COMMA',
        item: '<',
        item1: ',',
        station: 'main'
    },
    {
        id: 81,
        meth: 'KC_DOT',
        item: '>',
        item1: '.',
        station: 'main'
    },
    {
        id: 82,
        meth: 'KC_SLASH',
        item: '?',
        item1: '/',
        station: 'main'
    },
    {
        id: 83,
        meth: 'KC_DELETE',
        item: 'Delete',
        station: 'middle'
    },
    {
        id: 84,
        meth: 'KC_RIGHT_SHIFT',
        item: 'R-Shift',
        station: 'main',
        width: '144px'
    },
    {
        id: 85,
        meth: 'KC_END',
        item: 'End',
        station: 'middle'
    },
    {
        id: 86,
        meth: 'KC_KP_1',
        item: '1',
        station: 'number'
    },
    {
        id: 87,
        meth: 'KC_KP_2',
        item: '2',
        station: 'number'
    },
    {
        id: 88,
        meth: 'KC_KP_3',
        item: '3',
        station: 'number'
    },
    {
        id: 89,
        meth: 'KC_KP_ENTER',
        item: 'Num',
        item1: 'Enter',
        station: 'lnumber',
        heght: '100px'
    },
    {
        id: 90,
        meth: 'KC_LEFT_CTRL',
        item: 'L-Ctrl',
        station: 'main',
        width: '60px'
    },
    {
        id: 91,
        meth: 'KC_LEFT_GUI',
        item: 'L-Win',
        station: 'main',
        width: '60px'
    },
    {
        id: 92,
        meth: 'KC_LEFT_ALT',
        item: 'L-Alt',
        station: 'main',
        width: '60px'
    },
    {
        id: 93,
        meth: 'KC_SPACE',
        item: 'Space',
        station: 'main',
        width: '328px'
    },
    {
        id: 94,
        meth: 'KC_RIGHT_ALT',
        item: 'R-Alt',
        station: 'main',
        width: '60px'
    },
    {
        id: 95,
        meth: 'KC_RIGHT_GUI',
        item: 'R-Win',
        station: 'main',
        width: '60px'
    },
    {
        id: 96,
        meth: 'KC_MENU',
        item: 'Menu',
        station: 'main',
        width: '60px'
    },
    {
        id: 97,
        meth: 'KC_RIGHT_CTRL',
        item: 'R-Ctrl',
        station: 'main',
        width: '60px'
    },
    {
        id: 98,
        meth: 'KC_PAGE_DOWN',
        item: 'Page',
        item1: 'Down',
        station: 'middle'

    },
    {
        id: 98.1,
        meth: '',
        item: '',
        station: 'middle'
    },
    {
        id: 98.2,
        meth: '',
        item: '',
        station: 'middle'
    },
    {
        id: 98.3,
        meth: '',
        item: '',
        station: 'middle'
    },
    {
        id: 98.4,
        meth: '',
        item: '',
        station: 'middle'
    },
    {
        id: 99,
        meth: 'KC_UP',
        item: '↑',
        station: 'middle'
    },
    {
        id: 99.1,
        meth: '',
        item: '',
        station: 'middle'
    },
    {
        id: 100,
        meth: 'KC_LEFT',
        item: '←',
        station: 'middle'
    },

    {
        id: 101,
        meth: 'KC_DOWN',
        item: '↓',
        station: 'middle'

    },
    {
        id: 102,
        meth: 'KC_RIGHT',
        item: '→',
        station: 'middle'
    },
    {
        id: 103,
        meth: 'KC_KP_0',
        item: '0',
        item1: 'Ins',
        station: 'number',
        width: '100px'
    },
    {
        id: 104,
        meth: 'KC_KP_DOT',
        item: '.',
        item1: 'Del',
        station: 'number'
    },
    {
        id: 105,
        meth: 'KC_NO',
        item: ' ',
        station: 'specil'
    },
    {
        id: 106,
        meth: 'KC_TRNS',
        item: '▽',
        station: 'specil'
    },
    {
        id: 107,
        meth: 'KC_SHIFT_AND_GRAVE',
        item: '~',
        station: 'specil'
    },
    {
        id: 108,
        meth: 'KC_SHIFT_AND_1',
        item: '!',
        station: 'specil'
    },
    {
        id: 109,
        meth: 'KC_SHIFT_AND_2',
        item: '@',
        station: 'specil'
    },
    {
        id: 110,
        meth: 'KC_SHIFT_AND_3',
        item: '#',
        station: 'specil'
    },
    {
        id: 111,
        meth: 'KC_SHIFT_AND_4',
        item: '$',
        station: 'specil'
    },
    {
        id: 112,
        meth: 'KC_SHIFT_AND_5',
        item: '%',
        station: 'specil'
    },
    {
        id: 113,
        meth: 'KC_SHIFT_AND_6',
        item: '^',
        station: 'specil'
    },
    {
        id: 114,
        meth: 'KC_SHIFT_AND_7',
        item: '&',
        station: 'specil'
    },
    {
        id: 115,
        meth: 'KC_SHIFT_AND_8',
        item: '*',
        station: 'specil'
    },
    {
        id: 115.5,
        meth: 'KC_KP_EQUAL',
        item: '=',
        station: 'specil'
    },
    {
        id: 116,
        meth: 'KC_SHIFT_AND_9',
        item: '(',
        station: 'specil'
    },
    {
        id: 117,
        meth: 'KC_SHIFT_AND_0',
        item: ')',
        station: 'specil'
    },
    {
        id: 118,
        meth: 'KC_SHIFT_AND_MINUS',
        item: '_',
        station: 'specil'
    },
    {
        id: 119,
        meth: 'KC_SHIFT_AND_EQUAL',
        item: '+',
        station: 'specil'
    },
    {
        id: 120,
        meth: 'KC_SHIFT_AND_LEFT_BRACKET',
        item: '{',
        station: 'specil'
    },
    {
        id: 121,
        meth: 'KC_SHIFT_AND_RIGHT_BRACKET',
        item: '}',
        station: 'specil'
    },
    {
        id: 122,
        meth: 'KC_SHIFT_AND_COMMA',
        item: '<',
        station: 'specil'
    },
    {
        id: 123,
        meth: 'KC_SHIFT_AND_PERIOD',
        item: '>',
        station: 'specil'
    },
    {
        id: 124,
        meth: 'KC_SHIFT_AND_SEMICOLON',
        item: ':',
        station: 'specil'
    },
    {
        id: 125,
        meth: 'KC_SHIFT_AND_BACKSLASH',
        item: '|',
        station: 'specil'
    },
    {
        id: 126,
        meth: 'KC_SHIFT_AND_SLASH',
        item: '?',
        station: 'specil'
    },
    {
        id: 127,
        meth: 'KC_SHIFT_AND_APOSTROPHE',
        item: '\"',
        station: 'specil'
    },
    {
        id: 128,
        meth: 'KC_KANA',
        item: 'かな',
        station: 'specil'
    },
    {
        id: 129,
        meth: 'KC_HENK',
        item: '變換',
        station: 'specil'
    },
    {
        id: 130,
        meth: 'KC_MHEN',
        item: '無變換',
        station: 'specil'
    },
    {
        id: 131,
        meth: 'KC_HAEN',
        item: '한영',
        station: 'specil'
    },
    {
        id: 132,
        meth: 'KC_HANJ',
        item: '漢字',
        station: 'specil'
    },
    {
        id: 133,
        meth: 'Reset',
        item: 'Reset',
        station: 'specil'
    }
]



export default keyDate;