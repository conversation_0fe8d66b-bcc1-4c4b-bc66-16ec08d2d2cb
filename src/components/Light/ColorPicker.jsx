import React, { useRef, useEffect, useState } from 'react';
import iro from '@jaames/iro';

const ColorPicker = ({ onColorChange, color }) => {
  const colorPickerRef = useRef(null);
  const [colorPicker, setColorPicker] = useState(null);
  useEffect(() => {
    // Initialize color picker
    if (colorPickerRef.current) {
      const picker = new iro.ColorPicker(colorPickerRef.current, {
        width: 220,
        color: `rgb(${color[0]}, ${color[1]}, ${color[2]})`,
        layout: [
          {
            component: iro.ui.Wheel, // 颜色圆盘
            options: {},
          },
          {
            component: iro.ui.Slider, // 滑块组件
            options: {
              sliderType: 'value', // 明度滑块
              // 可选值:
              // 'hue' - 色相滑块
              // 'saturation' - 饱和度滑块
              // 'value' - 明度滑块
              // 'alpha' - 透明度滑块
            },
          },
        ],
      });

      // Add color change listener
      const colorChangeHandler = (color) => {
        if (onColorChange) {
          onColorChange(color);
        }
      };
      picker.on('color:change', colorChangeHandler);

      setColorPicker(picker);

      // Cleanup
      return () => {
        picker.off('color:change', colorChangeHandler);
      };
    }
  }, []); // Remove dependencies to prevent reinitializing

  return (
    <div>
      <div ref={colorPickerRef}></div>
    </div>
  );
};

export default ColorPicker;
