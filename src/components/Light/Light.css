.mater_inputs {
   /* height: 100px; */
   display: flex;
   width: 200px !important;
   height: auto !important;
   padding: 0 !important;
   background-color: #17181C !important;
   gap: 10px;
}
.flexbox-fix{
    /* display: block !important; */
    padding-top: 0px !important;
}
.mater_inputs input{
    border-width: 0 0 0 !important;
    font-size: 12px !important;
    color: #b0b0b0 !important;
    box-sizing: initial;
    padding: 5px;
    width: 60px !important;
    background-color: #000000 !important;
    margin-top: 12px !important;
    /* border: 1px solid blue !important; */
}
.w-color-editable-input-rgba .w-color-editable-input input{
    font-size: 12px !important;
    width: 80% !important;
    border-width: 0 0 0 !important;
    background-color: black !important;
    border-radius: 3px;
    padding: 5px !important;
    padding: 0 5px !important;
}
.flexbox-fix input{
    font-size: 12px !important;
    width: 80% !important;
    border-width: 0 0 0 !important;
    background-color: black !important;
    border-radius: 3px;
    padding: 12px 12px 0px !important;
    /* border: 1px solid blue !important; */
}
.input_color{
    margin-bottom: 30px;
    background-color: #17181C !important;
    display: flex;
    flex-direction: column;
}
.mater_inputs label{
    color: #ffffff !important;
    top: -10px !important;
    left: 30% !important;
    font-size: 15px !important;
    text-transform: uppercase  !important;
}
.mater_inputs span{
    color: #ffffff !important;
    top: -10px !important;
    left: 30% !important;
    font-size: 15px !important;
    text-transform: uppercase  !important;
    width: 80%;
    text-align: center;
}
.circle-show{
    width: 28px;
    height: 28px;
    border-radius: 50%;
    overflow: hidden;
}