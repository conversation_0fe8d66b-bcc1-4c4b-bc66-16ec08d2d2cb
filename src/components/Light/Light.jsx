import { Slider,ColorPicker } from 'antd';
import { get256HSV, rgbToHex } from '../../utils/colorCoversion';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { KeyboardContext } from '../Keyboard/KeyboardContext';
import { useContext, useState } from 'react';
// import { MaterialPicker} from 'react-color';
import { Wheel,Material ,Circle} from '@uiw/react-color';
import LightIcon from './LightIcon';
import { useTranslation } from 'react-i18next';
import './Light.css'


const Light = () => {
  const { t } = useTranslation();
  const { data } = useContext(KeyboardContext);
  const { backlight, setBacklight, addToQueue } = useHandleDevice();
  const [colorHex, setColorHex] = useState('#ffffff');
  const selectedKeys = data.currentSelectedKeycaps;


  const [colorRgb, setColorRgb] = useState({
    r: 241,
    g: 112,
    b: 19,
    a: 1,
  });
  // const [hexColor, setHexColor] = useState('#f17013');

  // const handleColorChange123 = (newColor) => {
  //   setColor(newColor.rgb);
  //   setHexColor(newColor.hex);
  // };

  const handleBrightnessChange = (value) => {
    setBacklight({...backlight, brightness: value});
    const instruction = `07 03 01 ${Number(value).toString(16).padStart(2, '0')} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`;
    addToQueue(instruction);
  }

  const handleSpeedChange = (value) => {
    setBacklight({...backlight, speed: value});
    const instruction = `07 03 03 ${Number(value).toString(16).padStart(2, '0')} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`;
    addToQueue(instruction);
  }

  const handleEffectChange = (value) => {
    setBacklight({...backlight, mode: value});
    const instruction = `07 03 02 ${Number(value).toString(16).padStart(2, '0')} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`;
    addToQueue(instruction);
  }

  const handleColorChange = (color) => {
    const hsv_arr = get256HSV(color.hex);
    if (backlight.mode === 46) {
      selectedKeys.forEach(key => {
        addToQueue(`37 ${key.row} ${key.column} ${Number(hsv_arr[0]).toString(16).padStart(2, '0')} ${Number(hsv_arr[1]).toString(16).padStart(2, '0')} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
      });
      addToQueue(`41 01`);
    } else {
      const instruction = `07 03 04 ${Number(hsv_arr[0]).toString(16).padStart(2, '0')} ${Number(hsv_arr[1]).toString(16).padStart(2, '0')} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`;
      addToQueue(instruction);
    }
  }

  return(
    <div style={{display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center'}}>
      <div style={{width: '100%', maxWidth: '1120px', marginTop: '2em'}}>
        <div className="d-flex align-items-center">
          <div style={{height: '1.5em', width: '0.2em', borderRadius: '0.25em', background: 'var(--bs-primary)', marginRight: '0.5em'}}></div>
          <div style={{fontSize: '1.2em', fontWeight: 'bold'}}>{t('light.light_preset')}</div>
        </div>
      </div>
      <div className="d-flex">
        <div
        style={{
          marginRight: '1em',
          padding: '1em',
        }}
          className='custom-card-container'
          onTouchMove={(e) => {
            e.currentTarget.style.overflow = 'scroll';
          }}
        >
          {[
            { key: 0, name: t('light.light_mode.0'), icon: '1' },
            { key: 46, name: t('light.light_mode.46'), icon: '2' },
            { key: 1, name: t('light.light_mode.1'), icon: '2' },
            { key: 2, name: t('light.light_mode.2'), icon: '⌨️' },
            { key: 3, name: t('light.light_mode.3'), icon: '↕️' },
            { key: 4, name: t('light.light_mode.4'), icon: '↔️' },
            { key: 5, name: t('light.light_mode.5'), icon: '🌟' },
            { key: 6, name: t('light.light_mode.6'), icon: '🎨' },
            { key: 7, name: t('light.light_mode.7'), icon: '💡' },
            { key: 8, name: t('light.light_mode.8'), icon: '🌈' },
            { key: 9, name: t('light.light_mode.9'), icon: '✨' },
            { key: 10, name: t('light.light_mode.10'), icon: '🌀' },
            { key: 11, name: t('light.light_mode.11'), icon: '💫' },
            { key: 12, name: t('light.light_mode.12'), icon: '🎡' },
            { key: 13, name: t('light.light_mode.13'), icon: '↔️' },
            { key: 14, name: t('light.light_mode.14'), icon: '↕️' },
            { key: 15, name: t('light.light_mode.15'), icon: '🌈' },
            { key: 16, name: t('light.light_mode.16'), icon: '🔄' },
            { key: 17, name: t('light.light_mode.17'), icon: '⭕' },
            { key: 18, name: t('light.light_mode.18'), icon: '🎡' },
            { key: 19, name: t('light.light_mode.19'), icon: '🌀' },
            { key: 20, name: t('light.light_mode.20'), icon: '📡' },
            { key: 21, name: t('light.light_mode.21'), icon: '🌈' },
            { key: 22, name: t('light.light_mode.22'), icon: '🎡' },
            { key: 23, name: t('light.light_mode.23'), icon: '💧' },
            { key: 24, name: t('light.light_mode.24'), icon: '🌈' },
            { key: 25, name: t('light.light_mode.25'), icon: '🌟' },
            { key: 26, name: t('light.light_mode.26'), icon: '🔄' },
            { key: 27, name: t('light.light_mode.27'), icon: '〰️' },
            { key: 28, name: t('light.light_mode.28'), icon: '📱' },
            { key: 29, name: t('light.light_mode.29'), icon: '💫' },
            { key: 30, name: t('light.light_mode.30'), icon: '🔲' },
            { key: 31, name: t('light.light_mode.31'), icon: '🎯' },
            { key: 32, name: t('light.light_mode.32'), icon: '🔢' },
            { key: 33, name: t('light.light_mode.33'), icon: '💫' },
            { key: 34, name: t('light.light_mode.34'), icon: '✨' },
            { key: 35, name: t('light.light_mode.35'), icon: '🌟' },
            { key: 36, name: t('light.light_mode.36'), icon: '🎨' },
            { key: 37, name: t('light.light_mode.37'), icon: '❌' },
            { key: 38, name: t('light.light_mode.38'), icon: '🎯' },
            { key: 39, name: t('light.light_mode.39'), icon: '⚡' },
            { key: 40, name: t('light.light_mode.40'), icon: '🌈' },
            { key: 41, name: t('light.light_mode.41'), icon: '💦' },
            { key: 42, name: t('light.light_mode.42'), icon: '🎨' },
            { key: 43, name: t('light.light_mode.43'), icon: '💫' },
            { key: 44, name: t('light.light_mode.44'), icon: '✨' }
          ].map(effect => (
            <div
              key={effect.key}
              onClick={() => handleEffectChange(effect.key)}
              style={{
                width: '100px',
                height: '100px',
                background: backlight.mode === effect.key ? 'var(--bs-primary)' : '#1C1D22',
                borderRadius: '0.5em',
                margin: '0.5em',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'background 0.2s',
                textAlign: 'center'
              }}
            >
              <div style={{fontSize: '1.5em', fontWeight: 'bold'}}><LightIcon currentColor="#EFF0F5" currentEffect={effect.key} /></div>
              <div style={{fontSize: '0.9em', marginTop: '0.5em', color: backlight.mode === effect.key ? '#fff' : '#888'}}>{effect.name}</div>
            </div>
          ))}
        </div>
        <div
          style={{
            width: '700px',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          className='custom-card-container'
          onTouchMove={(e) => {
            e.currentTarget.style.overflow = 'scroll';
          }}
        >
{/*
          <ColorPicker
             value={colorHex}

          /> */}
          <div >

            <div className='input_color'>
              <Material
                 className='mater_inputs'
                 color={colorHex}
                 onChange={(color) => {
                 setColorHex(color.hex);
                // setColorRgb(color.rgb);
                 clearTimeout(window.colorChangeTimer);
                window.colorChangeTimer = setTimeout(() => {
                  handleColorChange(color);
                }, 100);
                }}
              />

             {/* <ColorPicker
             className='eee'
             style={{width: 'fit-content'}}
             value={colorHex}
             disabled

            /> */}
            <div className='circle-show'  style = {{
              backgroundColor: colorHex
            }}></div>
          </div>
           <Wheel
             color={colorHex}
             onChange={(color) => {
               setColorHex(color.hex);
               setColorRgb(color.rgb);
               clearTimeout(window.colorChangeTimer);
               window.colorChangeTimer = setTimeout(() => {
                 handleColorChange(color);
               }, 100);
             }}
           />
          </div>


           {/* <div>HEX: {colorHex}</div>

           <div>RGB: ({colorRgb.r}, {colorRgb.g}, {colorRgb.b})</div> */}



          <div style={{ marginLeft: '4em' }}>
            <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '2em'}}>
              <div style={{fontSize: '1.2em', fontWeight: 'bold', marginRight: '1em'}}>{t('light.global_brightness')}</div>
              <div style={{width: '15em'}}>
                <Slider
                  defaultValue={Math.round((backlight.brightness / 255) * 100)}
                  max={100}
                  min={0}
                  onChange={(value) => handleBrightnessChange(Math.round((value / 100) * 255))}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
              </div>
            </div>
            {
              (backlight.mode !== 46 && backlight.mode !== 1) && (
                <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                  <div style={{fontSize: '1.2em', fontWeight: 'bold', marginRight: '1em'}}>{t('light.global_speed')}</div>
                  <div style={{width: '15em'}}>
                    <Slider
                      defaultValue={Math.round((backlight.speed / 255) * 100)}
                      max={100}
                      min={0}
                      onChange={(value) => handleSpeedChange(Math.round((value / 100) * 255))}
                      tooltip={{ formatter: (value) => `${value}%` }}
                    />
                  </div>
                </div>
              )
            }
          </div>
        </div>

      </div>
    </div>
  )
}

export default Light;