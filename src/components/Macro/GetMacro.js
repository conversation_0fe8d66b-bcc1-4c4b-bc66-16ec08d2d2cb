const GetMacro = (dataQueue) => {
  // 获取键盘总的最大可设置macro数目 0x0C
  dataQueue.addToQueue("0C 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00");
  
  // 获取键盘macro缓存大小 0x0D
  dataQueue.addToQueue("0D 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00");
  
  // 获取键盘macro配置 0x0E - 分批获取所有macro数据
  // 从偏移0开始，每次获取28字节数据
  for (let offset = 0; offset < 1024; offset += 28) {
    const offsetHigh = (offset >> 8) & 0xFF;
    const offsetLow = offset & 0xFF;
    const dataLength = Math.min(28, 1024 - offset);
    
    const offsetHighHex = offsetHigh.toString(16).padStart(2, '0').toUpperCase();
    const offsetLowHex = offsetLow.toString(16).padStart(2, '0').toUpperCase();
    const dataLengthHex = dataLength.toString(16).padStart(2, '0').toUpperCase();
    
    dataQueue.addToQueue(`0E ${offsetHighHex} ${offsetLowHex} ${dataLengthHex} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
  }
};

export default GetMacro;
