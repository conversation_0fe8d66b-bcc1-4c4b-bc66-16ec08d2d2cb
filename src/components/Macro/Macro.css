.macro-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 20px;
  /* min-height: 100vh; */
  /* background: #161720; */
}

.macro-content {
  width: 100%;
  max-width: 1120px;
  margin-top: 20px;
}

.macro-list {
  margin-bottom: 20px;
}

.macro-list .ant-card {
  background: #1C1D22;
  border: 1px solid #2A2B30;
  border-radius: 8px;
}

.macro-list .ant-card-head {
  background: #1C1D22;
  border-bottom: 1px solid #2A2B30;
}

.macro-list .ant-card-head-title {
  color: #FFFFFF;
  font-weight: bold;
}

.macro-list .ant-card-body {
  color: #B0B0B0;
}

.macro-list .ant-card-actions {
  background: #1C1D22;
  border-top: 1px solid #2A2B30;
}

.macro-list .ant-card-actions > li {
  margin: 4px 0;
}

.macro-list .ant-card-actions .ant-btn {
  font-size: 12px;
  height: 28px;
  padding: 0 8px;
}

.recording-panel {
  margin-top: 20px;
}

.recording-panel .ant-card {
  background: #1C1D22;
  border: 2px solid #FF4D4F;
  border-radius: 8px;
  animation: recording-pulse 2s infinite;
}

@keyframes recording-pulse {
  0% {
    border-color: #FF4D4F;
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
  }
  50% {
    border-color: #FF7875;
    box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
  }
  100% {
    border-color: #FF4D4F;
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}

.recording-panel .ant-card-head {
  background: #1C1D22;
  border-bottom: 1px solid #FF4D4F;
}

.recording-panel .ant-card-head-title {
  color: #FF4D4F;
  font-weight: bold;
}

.recording-panel .ant-card-body {
  color: #FFFFFF;
}

.macro-action-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  margin: 2px 0;
  background: #2A2B30;
  border-radius: 4px;
}

.macro-action-type {
  margin-right: 8px;
  font-weight: bold;
}

.macro-action-key {
  background: #3A3B40;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  margin-right: 8px;
}

.macro-action-time {
  color: #888;
  font-size: 12px;
  margin-left: auto;
}

.macro-stats {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  padding: 8px;
  background: #2A2B30;
  border-radius: 4px;
}

.macro-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.macro-stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #FFFFFF;
}

.macro-stat-label {
  font-size: 12px;
  color: #888;
}

.macro-empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.macro-empty-state .ant-empty-description {
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .macro-list .ant-list-grid .ant-col {
    max-width: 33.333333% !important;
    flex: 0 0 33.333333% !important;
  }
}

@media (max-width: 768px) {
  .macro-list .ant-list-grid .ant-col {
    max-width: 50% !important;
    flex: 0 0 50% !important;
  }

  .macro-list .ant-card-actions .ant-btn {
    font-size: 11px;
    height: 24px;
    padding: 0 6px;
  }
}

@media (max-width: 480px) {
  .macro-list .ant-list-grid .ant-col {
    max-width: 100% !important;
    flex: 0 0 100% !important;
  }
}

/* 按钮状态样式 */
.macro-record-btn.recording {
  background: #FF4D4F !important;
  border-color: #FF4D4F !important;
  animation: button-pulse 1s infinite;
}

@keyframes button-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.macro-play-btn:hover {
  background: #52C41A !important;
  border-color: #52C41A !important;
}

.macro-delete-btn:hover {
  background: #FF4D4F !important;
  border-color: #FF4D4F !important;
}

.macro-table-row {
  background-color: #17181C;
}

.ant-table-body {
  scrollbar-width: thin;
  scrollbar-color: #4e4e4e #17181C;
}