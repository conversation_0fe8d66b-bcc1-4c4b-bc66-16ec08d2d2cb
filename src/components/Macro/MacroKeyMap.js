// 按键码映射表 - 将JavaScript KeyCode映射到HID键盘扫描码
export const MacroKeyMap = {
  // 字母键
  65: 0x04, // A
  66: 0x05, // B
  67: 0x06, // C
  68: 0x07, // D
  69: 0x08, // E
  70: 0x09, // F
  71: 0x0A, // G
  72: 0x0B, // H
  73: 0x0C, // I
  74: 0x0D, // J
  75: 0x0E, // K
  76: 0x0F, // L
  77: 0x10, // M
  78: 0x11, // N
  79: 0x12, // O
  80: 0x13, // P
  81: 0x14, // Q
  82: 0x15, // R
  83: 0x16, // S
  84: 0x17, // T
  85: 0x18, // U
  86: 0x19, // V
  87: 0x1A, // W
  88: 0x1B, // X
  89: 0x1C, // Y
  90: 0x1D, // Z

  // 数字键
  49: 0x1E, // 1
  50: 0x1F, // 2
  51: 0x20, // 3
  52: 0x21, // 4
  53: 0x22, // 5
  54: 0x23, // 6
  55: 0x24, // 7
  56: 0x25, // 8
  57: 0x26, // 9
  48: 0x27, // 0

  // 功能键
  13: 0x28, // Enter
  27: 0x29, // Escape
  8: 0x2A,  // Backspace
  9: 0x2B,  // Tab
  32: 0x2C, // Space
  189: 0x2D, // -
  187: 0x2E, // =
  219: 0x2F, // [
  221: 0x30, // ]
  220: 0x31, // \
  186: 0x33, // ;
  222: 0x34, // '
  192: 0x35, // `
  188: 0x36, // ,
  190: 0x37, // .
  191: 0x38, // /

  // 修饰键
  16: 0xE1, // Shift
  17: 0xE0, // Ctrl
  18: 0xE2, // Alt
  91: 0xE3, // Win/Cmd

  // 方向键
  37: 0x50, // Left Arrow
  38: 0x52, // Up Arrow
  39: 0x4F, // Right Arrow
  40: 0x51, // Down Arrow

  // F键
  112: 0x3A, // F1
  113: 0x3B, // F2
  114: 0x3C, // F3
  115: 0x3D, // F4
  116: 0x3E, // F5
  117: 0x3F, // F6
  118: 0x40, // F7
  119: 0x41, // F8
  120: 0x42, // F9
  121: 0x43, // F10
  122: 0x44, // F11
  123: 0x45, // F12

  // 其他常用键
  45: 0x49,  // Insert
  46: 0x4C,  // Delete
  36: 0x4A,  // Home
  35: 0x4D,  // End
  33: 0x4B,  // Page Up
  34: 0x4E,  // Page Down
  20: 0x39,  // Caps Lock
  144: 0x53, // Num Lock
  145: 0x47, // Scroll Lock

  // 小键盘
  96: 0x62,  // Numpad 0
  97: 0x59,  // Numpad 1
  98: 0x5A,  // Numpad 2
  99: 0x5B,  // Numpad 3
  100: 0x5C, // Numpad 4
  101: 0x5D, // Numpad 5
  102: 0x5E, // Numpad 6
  103: 0x5F, // Numpad 7
  104: 0x60, // Numpad 8
  105: 0x61, // Numpad 9
  106: 0x55, // Numpad *
  107: 0x57, // Numpad +
  109: 0x56, // Numpad -
  110: 0x63, // Numpad .
  111: 0x54, // Numpad /
};

// 获取按键的HID扫描码
export const getHIDKeyCode = (jsKeyCode) => {
  return MacroKeyMap[jsKeyCode] || 0x00;
};

// 获取按键名称
export const getKeyName = (jsKeyCode) => {
  const keyNames = {
    // 字母键
    65: 'A', 66: 'B', 67: 'C', 68: 'D', 69: 'E', 70: 'F', 71: 'G', 72: 'H',
    73: 'I', 74: 'J', 75: 'K', 76: 'L', 77: 'M', 78: 'N', 79: 'O', 80: 'P',
    81: 'Q', 82: 'R', 83: 'S', 84: 'T', 85: 'U', 86: 'V', 87: 'W', 88: 'X',
    89: 'Y', 90: 'Z',
    
    // 数字键
    48: '0', 49: '1', 50: '2', 51: '3', 52: '4', 53: '5', 54: '6', 55: '7', 56: '8', 57: '9',
    
    // 功能键
    13: 'Enter', 27: 'Esc', 8: 'Backspace', 9: 'Tab', 32: 'Space',
    189: '-', 187: '=', 219: '[', 221: ']', 220: '\\',
    186: ';', 222: "'", 192: '`', 188: ',', 190: '.', 191: '/',
    
    // 修饰键
    16: 'Shift', 17: 'Ctrl', 18: 'Alt', 91: 'Win',
    
    // 方向键
    37: '←', 38: '↑', 39: '→', 40: '↓',
    
    // F键
    112: 'F1', 113: 'F2', 114: 'F3', 115: 'F4', 116: 'F5', 117: 'F6',
    118: 'F7', 119: 'F8', 120: 'F9', 121: 'F10', 122: 'F11', 123: 'F12',
    
    // 其他键
    45: 'Insert', 46: 'Delete', 36: 'Home', 35: 'End', 33: 'PgUp', 34: 'PgDn',
    20: 'CapsLock', 144: 'NumLock', 145: 'ScrollLock'
  };
  
  return keyNames[jsKeyCode] || `Key${jsKeyCode}`;
};

export default MacroKeyMap;
