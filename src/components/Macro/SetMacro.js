const SetMacro = (hexArray, setMacro) => {
  if (hexArray[0] === '0C') {
    // 获取键盘总的最大可设置macro数目响应
    const maxMacroCount = parseInt(hexArray[1], 16);
    setMacro(prev => ({ ...prev, maxMacroCount }));
  } else if (hexArray[0] === '0D') {
    // 获取键盘macro缓存大小响应
    const bufferSize = (parseInt(hexArray[1], 16) << 8) | parseInt(hexArray[2], 16);
    setMacro(prev => ({ ...prev, bufferSize }));
  } else if (hexArray[0] === '0E' || hexArray[0] === '0F') {
    // 获取键盘macro配置响应
    const offsetHigh = parseInt(hexArray[1], 16);
    const offsetLow = parseInt(hexArray[2], 16);
    const dataLength = parseInt(hexArray[3], 16);
    const offset = (offsetHigh << 8) | offsetLow;

    // 更新macro数据缓存
    setMacro(prev => {
      const newBuffer = { ...prev.buffer };
      for (let i = 0; i < dataLength; i++) {
        newBuffer[offset + i] = hexArray[4 + i];
      }
      return { ...prev, buffer: newBuffer };
    });
  }
};

// 设置宏键配置数据（分批，每批28字节数据）
export const setMacroConfig = (dataQueue, offset, data) => {
  resetMacroConfig(dataQueue);
  const CHUNK_SIZE = 28; // 每批28字节
  let remaining = data.length;
  let chunkOffset = 0;

  while (remaining > 0) {
    const chunk = data.slice(chunkOffset, chunkOffset + CHUNK_SIZE);
    const chunkLength = chunk.length;

    const currentOffset = offset + chunkOffset;
    const offsetHigh = (currentOffset >> 8) & 0xFF;
    const offsetLow = currentOffset & 0xFF;
    const offsetHighHex = offsetHigh.toString(16).padStart(2, '0').toUpperCase();
    const offsetLowHex = offsetLow.toString(16).padStart(2, '0').toUpperCase();
    const chunkLengthHex = chunkLength.toString(16).padStart(2, '0').toUpperCase();

    // 构建命令：0x0F + 偏移高8位 + 偏移低8位 + 数据长度 + 数据
    let command = `0F ${offsetHighHex} ${offsetLowHex} ${chunkLengthHex}`;

    // 添加数据
    chunk.forEach(byte => {
      command += ` ${byte.toString(16).padStart(2, '0').toUpperCase()}`;
    });

    // 补足32字节
    const currentLength = 4 + chunkLength;
    for (let i = currentLength; i < 32; i++) {
      command += ' 00';
    }
    dataQueue.addToQueue(command);

    chunkOffset += CHUNK_SIZE;
    remaining -= chunkLength;
  }
};

// 重置宏键配置
export const resetMacroConfig = (dataQueue) => {
  dataQueue.addToQueue("10 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00");
};

// 编码宏键数据
export const encodeMacroData = (macroActions) => {
  const data = [];

  macroActions.forEach(action => {
    if (action.type === 'keydown' || action.type === 'keyup') {
      // 1. 起始标志
      data.push(0x01);
      // 2. 动作类型
      data.push(action.type === 'keydown' ? 0x02 : 0x03);
      // 5. 按键编码（HID码，转为16进制）
      let keyCodeHex = Number(action.keyCode);
      if (isNaN(keyCodeHex)) keyCodeHex = 0;
      data.push(keyCodeHex & 0xFF);
    } else if (action.type === 'delay') {
      // 只插入延迟，不带按键信息
      // 1. 起始标志
      data.push(0x01);
      // 2. 动作类型（0x04代表延迟）
      data.push(0x04);
      // 6-7. 延迟（ASCII "50" = 50ms）
      let delayStr = "00";
      if (action.duration && typeof action.duration === "number") {
        let ms = Math.round(action.duration);
        delayStr = ms.toString();
      }
      for (let i = 0; i < delayStr.length; i++) {
        data.push(delayStr.charCodeAt(i));
      }
      // 8. 分隔符
      data.push(0x7C);
    }
  });
  return data;
};

// 解析宏键数据，传入macro.buffer，返回macroList（M0~M15，每个含actions）
export const parseMacroBuffer = (buffer) => {
  // buffer: { [offset: number]: string }  // 16进制字符串
  // 返回macroList: [{actions: [...]}, ...] 共16个宏
  const macroList = [];
  // 先将buffer转为有序数组
  const keys = Object.keys(buffer)
    .map(k => parseInt(k, 10))
    .sort((a, b) => a - b);

  let i = 0;
  for (let macroIdx = 0; macroIdx < 16; macroIdx++) {
    const actions = [];
    while (i < keys.length) {
      const idx = keys[i];
      const byteStr = buffer[idx];
      const byte = parseInt(byteStr, 16);

      if (byte === 0x00) {
        // 00为当前宏结束
        i++;
        break;
      }

      if (byte === 0x01) {
        // 起始标志
        const typeIdx = keys[i + 1];
        const typeByte = parseInt(buffer[typeIdx], 16);

        if (typeByte === 0x02 || typeByte === 0x03) {
          // 按键动作
          const keyCodeIdx = keys[i + 2];
          const keyCode = parseInt(buffer[keyCodeIdx], 16);
          actions.push({
            type: typeByte === 0x02 ? 'keydown' : 'keyup',
            keyCode: keyCode,
          });
          i += 3;
        } else if (typeByte === 0x04) {
          // 延迟动作
          // 读取后续ASCII码直到遇到0x7C
          let delayStr = '';
          let j = i + 2;
          while (j < keys.length) {
            const asciiIdx = keys[j];
            const asciiByte = parseInt(buffer[asciiIdx], 16);
            if (asciiByte === 0x7C) {
              break;
            }
            delayStr += String.fromCharCode(asciiByte);
            j++;
          }
          const duration = parseInt(delayStr, 10) || 0;
          actions.push({
            type: 'delay',
            duration,
          });
          // 跳过到分隔符后
          i = j + 1;
        } else {
          // 未知类型，跳过
          i++;
        }
      } else {
        // 非起始标志，跳过
        i++;
      }
    }
    macroList.push({ actions });
  }
  return macroList;
};

export default SetMacro;
