const GetPerformance = (dataQueue) => {
  var rowIndex, colIndex;
  // 配列
  var rows = [17, 17, 17, 17, 17, 17]
  for (rowIndex = 0; rowIndex < rows.length; rowIndex++) {
    for (colIndex = 0; colIndex < rows[rowIndex]; colIndex++) {
      // 根据行数和列数生成编码
      let rowHex = rowIndex.toString(16).padStart(2, '0').toUpperCase();
      let colHex = colIndex.toString(16).padStart(2, '0').toUpperCase();
      dataQueue.addToQueue(`1A ${rowHex} ${colHex} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`)
    }
  }
};

export default GetPerformance;