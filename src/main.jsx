import React from 'react'
import { initReactI18next } from 'react-i18next'
import i18next from 'i18next'
import ReactDOM from 'react-dom/client'
import { i18nAlly } from 'vite-plugin-i18n-ally/client'
import LanguageDetector from 'i18next-browser-languagedetector';
import App from './App'
import "virtual:svg-icons-register"; //必须引入


const supportedLanguages = ['zh-CN', 'zh-TW', 'en-US', 'ja-JP', 'ko-KR', 'pt-PT']
const navigatorLanguage = navigator.language.includes('ja') ? 'ja-JP' : navigator.language.includes('ko') ? 'ko-KR' : navigator.language.includes('pt') ? 'pt-PT' : navigator.language
const fallbackLng = supportedLanguages.includes(navigatorLanguage) ? navigatorLanguage : 'en-US'
const root = ReactDOM.createRoot(document.querySelector('#root'))

const { asyncLoadResource } = i18nAlly({
  onInit({ language }) {
    i18next.use(LanguageDetector).use(initReactI18next).init({
      returnNull: false,
      react: {
        useSuspense: true,
      },
      debug: import.meta.env.DEV,
      resources: {},
      keySeparator: '.',
      interpolation: {
        escapeValue: false,
      },
      lowerCaseLng: false,
      fallbackLng,
    })
  },
  onInited(...args) {
    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>,
    )
  },
  onResourceLoaded: (resources, { language }) => {
    i18next.addResourceBundle(language, i18next.options.defaultNS[0], resources)
  },
  fallbackLng,
  detection: [
    {
      detect: 'cookie',
      lookup: 'without-namespace-lang',
    },
    {
      detect: 'htmlTag',
    },
  ],
})

const changeLanguage = i18next.changeLanguage
i18next.changeLanguage = async (lang, ...args) => {
  await asyncLoadResource(lang)
  return changeLanguage(lang, ...args)
}