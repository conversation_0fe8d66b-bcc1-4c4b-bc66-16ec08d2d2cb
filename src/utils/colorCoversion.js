export const getColorByte = (color) => {
  const cleanedColor = color.replace('#', '');
  const r = parseInt(cleanedColor[0], 16) * 16 + parseInt(cleanedColor[1], 16);
  const g = parseInt(cleanedColor[2], 16) * 16 + parseInt(cleanedColor[3], 16);
  const b = parseInt(cleanedColor[4], 16) * 16 + parseInt(cleanedColor[5], 16);
  return [r, g, b];
};

export const getHSV = (color) => {
  const [rPrime, gPrime, bPrime] = getColorByte(color).map((c) => c / 255);
  const [cmax, cmin] = [
    Math.max(rPrime, gPrime, bPrime),
    Math.min(rPrime, gPrime, bPrime),
  ];
  const delta = cmax - cmin;
  let h = 60;
  let s = 0;
  let v = cmax;
  if (delta === 0) {
    h = h * 0;
  } else if (cmax === rPrime) {
    h = h * (((gPrime - bPrime) / delta) % 6);
  } else if (cmax === gPrime) {
    h = h * ((bPrime - rPrime) / delta + 2);
  } else if (cmax === bPrime) {
    h = h * ((rPrime - gPrime) / delta + 4);
  }
  if (cmax !== 0) {
    s = delta / cmax;
  }
  return [(h + 360) % 360, s, v];
};

export const get256HSV = (color) => {
  const [h, s, v] = getHSV(color);
  return [
    Math.round((255 * h) / 360),
    Math.round(255 * s),
    Math.round(255 * v),
  ];
};

export const getHex = (hue, sat) => {
  let [r, g, b] = hsToRgb({hue, sat}).map((x) => x.toString(16));
  if (r.length == 1) r = '0' + r;
  if (g.length == 1) g = '0' + g;
  if (b.length == 1) b = '0' + b;
  return '#' + r + g + b;
}

export const hsToRgb = (hue, sat) => {
  try {
    sat = sat / 255;
    hue = Math.round(360 * hue) / 255;
    const c = sat;
    const x = c * (1 - Math.abs(((hue / 60) % 2) - 1));
    const m = 1 - c;
    const [r, g, b] = getRGBPrime(hue, c, x).map((n) =>
      Math.round(255 * (m + n)),
    );

    return [r, g, b];
  } catch (error) {
    console.error('Error converting HSV to RGB:', error);
    return [255, 255, 255]; // Return white if there's an error
  }
}

export const rgbToHex = (r, g, b) => {
  return '#' + r.toString(16).padStart(2, '0') + g.toString(16).padStart(2, '0') + b.toString(16).padStart(2, '0');
}

export const getRGBPrime = (hue, c, x) => {
  if (hue >= 0 && hue < 60) {
    return [c, x, 0];
  } else if (hue >= 60 && hue < 120) {
    return [x, c, 0];
  } else if (hue >= 120 && hue < 180) {
    return [0, c, x];
  } else if (hue >= 180 && hue < 240) {
    return [0, x, c];
  } else if (hue >= 240 && hue < 300) {
    return [x, 0, c];
  } else if (hue >= 300 && hue < 360) {
    return [c, 0, x];
  } else if (hue === 360) {
    return [c, x, 0];
  }
}